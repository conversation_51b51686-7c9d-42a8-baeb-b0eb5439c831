/**
 * 初始化状态管理上下文
 * 统一管理组件初始化状态
 */

import { provide, inject, reactive, type InjectionKey } from 'vue';

/**
 * 组件初始化状态枚举
 */
export enum InitializationState {
  PENDING = 'pending',
  LOADING = 'loading',
  READY = 'ready',
  ERROR = 'error',
}

/**
 * 初始化上下文接口
 */
export interface InitializationContext {
  /** 组件初始化状态映射 */
  componentStates: Record<string, InitializationState>;

  /** 注册组件初始化 */
  registerComponent: (componentId: string, initialState?: InitializationState) => void;

  /** 更新组件状态 */
  updateComponentState: (componentId: string, state: InitializationState) => void;

  /** 获取组件状态 */
  getComponentState: (componentId: string) => InitializationState;

  /** 等待所有组件就绪 */
  waitForAllReady: () => Promise<void>;

  /** 等待特定组件就绪 */
  waitForComponent: (componentId: string) => Promise<void>;

  /** 等待多个组件就绪 */
  waitForComponents: (componentIds: string[]) => Promise<void>;

  /** 检查是否所有组件都已就绪 */
  isAllReady: () => boolean;

  /** 检查特定组件是否就绪 */
  isComponentReady: (componentId: string) => boolean;

  /** 重置组件状态 */
  resetComponent: (componentId: string) => void;

  /** 重置所有组件状态 */
  resetAll: () => void;

  /** 获取所有组件状态统计 */
  getStateStats: () => {
    total: number;
    pending: number;
    loading: number;
    ready: number;
    error: number;
  };
}

/**
 * 初始化上下文键
 */
export const INITIALIZATION_CONTEXT_KEY: InjectionKey<InitializationContext> = Symbol('initializationContext');

/**
 * 提供初始化上下文
 */
export function provideInitializationContext(): InitializationContext {
  // 组件状态映射
  const componentStates = reactive<Record<string, InitializationState>>({});

  // 状态变化监听器
  const stateChangeListeners = new Map<string, Array<(state: InitializationState) => void>>();

  /**
   * 注册组件初始化
   */
  const registerComponent = (componentId: string, initialState: InitializationState = InitializationState.PENDING) => {
    componentStates[componentId] = initialState;
    console.log(`🔧 组件注册: ${componentId} -> ${initialState}`);
  };

  /**
   * 更新组件状态
   */
  const updateComponentState = (componentId: string, state: InitializationState) => {
    const oldState = componentStates[componentId];
    componentStates[componentId] = state;

    console.log(`🔄 状态更新: ${componentId} ${oldState} -> ${state}`);

    // 触发状态变化监听器
    const listeners = stateChangeListeners.get(componentId);
    if (listeners) {
      listeners.forEach((listener) => listener(state));
    }
  };

  /**
   * 获取组件状态
   */
  const getComponentState = (componentId: string): InitializationState => {
    return componentStates[componentId] || InitializationState.PENDING;
  };

  /**
   * 添加状态变化监听器
   */
  const addStateChangeListener = (componentId: string, listener: (state: InitializationState) => void) => {
    if (!stateChangeListeners.has(componentId)) {
      stateChangeListeners.set(componentId, []);
    }
    stateChangeListeners.get(componentId)!.push(listener);
  };

  /**
   * 等待特定组件就绪
   */
  const waitForComponent = (componentId: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      const currentState = getComponentState(componentId);

      if (currentState === InitializationState.READY) {
        resolve();
        return;
      }

      if (currentState === InitializationState.ERROR) {
        reject(new Error(`组件 ${componentId} 初始化失败`));
        return;
      }

      // 添加状态变化监听器
      addStateChangeListener(componentId, (state) => {
        if (state === InitializationState.READY) {
          resolve();
        } else if (state === InitializationState.ERROR) {
          reject(new Error(`组件 ${componentId} 初始化失败`));
        }
      });
    });
  };

  /**
   * 等待多个组件就绪
   */
  const waitForComponents = async (componentIds: string[]): Promise<void> => {
    const promises = componentIds.map((id) => waitForComponent(id));
    await Promise.all(promises);
    return void 0;
  };

  /**
   * 等待所有组件就绪
   */
  const waitForAllReady = (): Promise<void> => {
    const allComponentIds = Object.keys(componentStates);
    return waitForComponents(allComponentIds);
  };

  /**
   * 检查特定组件是否就绪
   */
  const isComponentReady = (componentId: string): boolean => {
    return getComponentState(componentId) === InitializationState.READY;
  };

  /**
   * 检查是否所有组件都已就绪
   */
  const isAllReady = (): boolean => {
    const allComponentIds = Object.keys(componentStates);
    return allComponentIds.every((id) => isComponentReady(id));
  };

  /**
   * 重置组件状态
   */
  const resetComponent = (componentId: string) => {
    updateComponentState(componentId, InitializationState.PENDING);
  };

  /**
   * 重置所有组件状态
   */
  const resetAll = () => {
    Object.keys(componentStates).forEach((id) => {
      resetComponent(id);
    });
  };

  /**
   * 获取所有组件状态统计
   */
  const getStateStats = () => {
    const states = Object.values(componentStates);
    return {
      total: states.length,
      pending: states.filter((s) => s === InitializationState.PENDING).length,
      loading: states.filter((s) => s === InitializationState.LOADING).length,
      ready: states.filter((s) => s === InitializationState.READY).length,
      error: states.filter((s) => s === InitializationState.ERROR).length,
    };
  };

  // 创建上下文对象
  const context: InitializationContext = {
    componentStates,
    registerComponent,
    updateComponentState,
    getComponentState,
    waitForAllReady,
    waitForComponent,
    waitForComponents,
    isAllReady,
    isComponentReady,
    resetComponent,
    resetAll,
    getStateStats,
  };

  // 提供上下文
  provide(INITIALIZATION_CONTEXT_KEY, context);

  return context;
}

/**
 * 使用初始化上下文
 */
export function useInitializationContext(): InitializationContext {
  const context = inject(INITIALIZATION_CONTEXT_KEY);

  if (!context) {
    throw new Error('useInitializationContext must be used within a component that provides InitializationContext');
  }

  return context;
}

/**
 * 可选的初始化上下文（不抛出错误）
 */
export function useInitializationContextOptional(): InitializationContext | null {
  return inject(INITIALIZATION_CONTEXT_KEY, null);
}

/**
 * 组件初始化Hook
 * 简化组件中的初始化状态管理
 */
export function useComponentInitialization(componentId: string) {
  const context = useInitializationContext();

  // 自动注册组件
  context.registerComponent(componentId);

  return {
    /** 设置为加载中 */
    setLoading: () => context.updateComponentState(componentId, InitializationState.LOADING),

    /** 设置为就绪 */
    setReady: () => context.updateComponentState(componentId, InitializationState.READY),

    /** 设置为错误 */
    setError: () => context.updateComponentState(componentId, InitializationState.ERROR),

    /** 获取当前状态 */
    getState: () => context.getComponentState(componentId),

    /** 检查是否就绪 */
    isReady: () => context.isComponentReady(componentId),

    /** 重置状态 */
    reset: () => context.resetComponent(componentId),
  };
}
