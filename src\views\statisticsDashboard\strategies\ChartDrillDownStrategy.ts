/**
 * 图表下探策略系统
 * 基于统一策略模式架构，实现图表下探功能的统一管理
 */

import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import type { commonQueryParams } from '../api';
import { BaseStrategy, type IBaseStrategy, BaseSingletonStrategyFactory } from './BaseStrategy';
import { createChartDrillDownStrategy } from './StrategyDependencyInjection';
import { getClueSourceChartConfigManager } from '../config/clueSourceChartConfig';

// ========== 核心接口定义 ==========

/**
 * 下探数据响应接口
 */
export interface DrillDownDataResponse {
  /** 下探数据 */
  data: ChartDataItem[];
  /** 配置更新 */
  configUpdate?: Partial<ChartConfig>;
  /** 是否为空数据 */
  isEmpty?: boolean;
}

/**
 * 图表下探策略接口
 * 继承通用策略接口，添加下探特有的方法
 */
export interface IChartDrillDownStrategy extends IBaseStrategy<ChartConfig, DrillDownDataResponse> {
  /** 支持的图表类型 */
  readonly supportedChartTypes: string[];

  /** 支持的最大下探层级 */
  readonly maxLevel: number;

  /** 获取下探数据 */
  fetchDrillDownData(parentData: ChartDataItem, targetLevel: number, chartConfig: ChartConfig): Promise<DrillDownDataResponse>;

  /** 更新图表配置（下探后） */
  updateChartConfig(
    data: ChartDataItem[],
    targetLevel: number,
    chartConfig: ChartConfig,
    parentData: ChartDataItem
  ): Partial<ChartConfig> | Promise<Partial<ChartConfig>>;

  /** 重置到顶层配置 */
  resetToTopLevelConfig(chartConfig: ChartConfig): Partial<ChartConfig> | Promise<Partial<ChartConfig>>;

  /** 验证下探操作 */
  validateDrillDown(parentData: ChartDataItem, targetLevel: number, chartConfig: ChartConfig): boolean;
}

// ========== 下探API装饰器 ==========

/**
 * 下探API调用装饰器接口
 */
export interface IDrillDownApiDecorator {
  /** 装饰下探API调用，自动注入筛选参数 */
  decorateDrillDownApiCall<T>(
    apiCall: (params: commonQueryParams, parentData: ChartDataItem, level: number) => Promise<T>,
    parentData: ChartDataItem,
    level: number
  ): Promise<T>;
}

// 装饰器已移除，不再需要兼容性导出

// ========== 图表下探策略抽象基类 ==========

/**
 * 图表下探策略抽象基类
 * 继承通用策略基类，实现下探特有的逻辑
 */
export abstract class BaseChartDrillDownStrategy extends BaseStrategy<ChartConfig, DrillDownDataResponse> implements IChartDrillDownStrategy {
  // 抽象属性 - 子类必须实现
  abstract readonly supportedChartTypes: string[];
  abstract readonly maxLevel: number;

  constructor() {
    super();
  }

  // 抽象方法 - 子类必须实现
  protected abstract fetchRawDrillDownData(
    params: commonQueryParams,
    parentData: ChartDataItem,
    level: number,
    chartConfig: ChartConfig
  ): Promise<any>;

  protected abstract transformDrillDownData(rawData: any, parentData: ChartDataItem, level: number): Promise<ChartDataItem[]>;

  /**
   * 获取配置ID
   */
  protected getConfigId(config: ChartConfig): string {
    return config.id;
  }

  /**
   * 实现基类的fetchApiData方法
   */
  protected async fetchApiData(_params: commonQueryParams, _config?: ChartConfig): Promise<any> {
    throw new Error('下探策略不使用标准的fetchApiData方法，请使用fetchDrillDownData');
  }

  /**
   * 实现基类的transformData方法
   */
  protected async transformData(_apiData: any): Promise<DrillDownDataResponse> {
    throw new Error('下探策略不使用标准的transformData方法，请使用transformDrillDownData');
  }

  /**
   * 支持的类型（实现基类要求）
   */
  get supportedTypes(): string[] {
    return this.supportedChartTypes;
  }

  /**
   * 模板方法：标准下探数据获取流程
   */
  async fetchDrillDownData(parentData: ChartDataItem, targetLevel: number, chartConfig: ChartConfig): Promise<DrillDownDataResponse> {
    // 前置验证
    if (!this.validateDrillDown(parentData, targetLevel, chartConfig)) {
      throw new Error(`下探验证失败: 图表=${chartConfig.id}, 层级=${targetLevel}`);
    }

    // 设置加载状态
    const loadingKey = `${chartConfig.id}_drill_${targetLevel}`;
    this.setLoadingState(loadingKey, true);

    try {
      console.log(`🎯 开始下探数据获取 [${this.strategyType}]: ${chartConfig.id} -> 层级${targetLevel}`);

      // 🔥 使用查询参数管理器获取参数
      const queryParamsManager = this.getQueryParamsManager();
      if (!queryParamsManager) {
        throw new Error(`🚨 ${this.strategyType} - 查询参数管理器不可用，请确保在主组件中正确注入`);
      }

      const params = queryParamsManager.getFinalParams();
      const rawData = await this.fetchRawDrillDownData(params, parentData, targetLevel, chartConfig);

      // 转换数据
      const drillDownData = await this.transformDrillDownData(rawData, parentData, targetLevel);

      // 生成配置更新 - 处理可能的Promise
      const configUpdateResult = this.updateChartConfig(drillDownData, targetLevel, chartConfig, parentData);
      let configUpdate: Partial<ChartConfig>;

      if (configUpdateResult instanceof Promise) {
        configUpdate = await configUpdateResult;
      } else {
        configUpdate = configUpdateResult;
      }

      console.log(`✅ 下探数据获取成功: ${chartConfig.id}, 层级${targetLevel}, 数据条数: ${drillDownData.length}`);

      return {
        data: drillDownData,
        configUpdate,
        isEmpty: drillDownData.length === 0,
      };
    } catch (error: any) {
      console.error(`❌ 下探数据获取失败 [${this.strategyType}]:`, error);
      console.error(`❌ 下探数据获取失败 [${this.strategyType}]:`, error);
      throw error;
    } finally {
      this.setLoadingState(loadingKey, false);
    }
  }

  /**
   * 默认配置更新实现 - 子类可以覆盖
   */
  updateChartConfig(
    data: ChartDataItem[],
    targetLevel: number,
    chartConfig: ChartConfig,
    parentData: ChartDataItem
  ): Partial<ChartConfig> | Promise<Partial<ChartConfig>> {
    return {
      customProps: {
        ...chartConfig.customProps,
        drillDownLevel: targetLevel,
        drillDownData: data,
        parentData,
        isDrillDown: true,
        loading: false,
      },
    };
  }

  /**
   * 默认重置配置实现 - 子类可以覆盖
   */
  resetToTopLevelConfig(chartConfig: ChartConfig): Partial<ChartConfig> | Promise<Partial<ChartConfig>> {
    return {
      customProps: {
        ...chartConfig.customProps,
        drillDownLevel: 0,
        drillDownData: undefined,
        parentData: undefined,
        isDrillDown: false,
        loading: false,
      },
    };
  }

  /**
   * 默认验证实现 - 子类可以覆盖
   */
  validateDrillDown(parentData: ChartDataItem, targetLevel: number, chartConfig: ChartConfig): boolean {
    // 基础验证
    if (!parentData) {
      console.warn('下探验证失败: 缺少父级数据');
      return false;
    }

    if (targetLevel > this.maxLevel) {
      console.warn(`下探验证失败: 超过最大层级 ${this.maxLevel}`);
      return false;
    }

    if (!this.supportedChartTypes.includes(chartConfig.type)) {
      console.warn(`下探验证失败: 不支持的图表类型 ${chartConfig.type}`);
      return false;
    }

    return true;
  }

  /**
   * 获取加载状态
   */
  public getLoadingState(configId: string): boolean {
    return super.getLoadingState(configId);
  }

  public getDrillDownLoadingState(chartId: string, level: number): boolean {
    const key = `${chartId}-${level}`;
    return this.getLoadingState(key);
  }
}

// ========== 具体策略实现：线索来源下探策略 ==========

/**
 * 线索来源图表下探策略
 */
export class ClueSourceDrillDownStrategy extends BaseChartDrillDownStrategy {
  readonly strategyType = 'clue-source-drill';
  readonly supportedChartTypes = ['bar', 'line'];
  readonly maxLevel = 1;

  protected async fetchRawDrillDownData(params: commonQueryParams, parentData: ChartDataItem, level: number, chartConfig: ChartConfig): Promise<any> {
    if (level === 1) {
      // 获取父级渠道键
      const parentChannelKey = parentData.channelKey || parentData.name;
      const originalDataSource = chartConfig.dataSource;

      // 根据父级渠道确定一级来源ID
      const parentSourceId = await this.getParentSourceId(parentChannelKey);
      if (!parentSourceId) {
        console.warn(`无法确定父级来源ID: ${parentChannelKey}`);
        return [];
      }

      console.log(`🎯 线索来源下探策略 - 二级下探API调用`);
      console.log(`📊 下探参数: 渠道=${parentChannelKey}, 来源ID=${parentSourceId}, 数据源=${originalDataSource}`);

      // 🔥 直接调用API获取数据，避免循环调用
      console.log(`🔄 直接调用API获取二级数据: parentSourceId=${parentSourceId}`);

      try {
        // 根据数据源类型选择合适的API
        let apiResponse: any;
        if (originalDataSource === 'valid') {
          const { queryValidClueSourceSecond } = await import('../api');
          apiResponse = await queryValidClueSourceSecond({ ...params, oneSourceId: parentSourceId });
        } else {
          const { queryAllClueSourceSecond } = await import('../api');
          apiResponse = await queryAllClueSourceSecond({ ...params, oneSourceId: parentSourceId });
        }

        console.log(`📊 API返回数据:`, apiResponse);

        // 使用现有的数据转换工具进行转换
        const { transformSecondApiDataToChartData } = await import('../utils/dataTransform');
        // 这里要用 parentSourceId
        const transformedData = transformSecondApiDataToChartData(apiResponse, parentSourceId);
        // 转换为下探数据格式 这里要用 parentKey
        return this.convertMonthlyDataToDrillDownData(transformedData, parentChannelKey);
      } catch (error) {
        console.error(`API调用失败:`, error);
        return [];
      }
    }

    throw new Error(`线索来源下探策略不支持层级: ${level}`);
  }

  protected async transformDrillDownData(rawData: any, _parentData: ChartDataItem, _level: number): Promise<ChartDataItem[]> {
    // rawData 已经是转换后的 ChartDataItem[]
    if (Array.isArray(rawData)) {
      return rawData;
    }

    console.warn('线索来源下探数据格式异常，返回空数组');
    return [];
  }

  /**
   * 根据父级渠道键获取对应的一级来源ID
   */
  private async getParentSourceId(parentChannelKey: string): Promise<string | null> {
    // 动态导入枚举，避免循环依赖
    const { ClueSourceEnum } = await import('../enums');

    // 渠道键到来源ID的映射 - 🔥 修复：使用正确的枚举值
    const mapping: Record<string, string> = {
      onlinePublic: ClueSourceEnum.ONE_SOURCE_ONLINE_PUB, // 1905168849350201344
      onlinePrivate: ClueSourceEnum.ONE_SOURCE_ONLINE_PRI, // 1904778848922349568
      offlinePrivate: ClueSourceEnum.ONE_SOURCE_OFFLINE_PRI, // 1905168849350201345
    };

    const sourceId = mapping[parentChannelKey] || null;
    console.log(`🔍 渠道映射: ${parentChannelKey} -> ${sourceId}`);

    return sourceId;
  }

  /**
   * 转换MonthlyChannelData为下探数据格式
   */
  private convertMonthlyDataToDrillDownData(monthlyData: any[], parentChannelKey: string): any[] {
    const drillDownData: any[] = [];

    try {
      console.log(`🔄 开始转换MonthlyChannelData为下探格式 - 父渠道: ${parentChannelKey}`);
      console.log(`📊 原始数据:`, monthlyData);

      if (!monthlyData || monthlyData.length === 0) {
        console.warn(`⚠️ 原始数据为空，无法转换`);
        return [];
      }

      monthlyData.forEach((monthItem) => {
        const subChannels = monthItem.subChannels?.[parentChannelKey];
        if (subChannels) {
          Object.entries(subChannels).forEach(([childKey, value]) => {
            if ((value as number) > 0) {
              // 计算百分比
              const percent = monthItem.value > 0 ? (((value as number) / monthItem.value) * 100).toFixed(1) : '0.0';

              drillDownData.push({
                name: monthItem.name,
                value: value as number,
                percent: percent + '%',
                channelKey: childKey,
                month: monthItem.name,
                parentChannel: parentChannelKey,
                level: 1,
              });
            }
          });
        }
      });

      console.log(`MonthlyChannelData转换完成，生成 ${drillDownData.length} 条下探数据`);
      return drillDownData;
    } catch (error) {
      console.error('MonthlyChannelData转换失败:', error);
      return [];
    }
  }

  /**
   * 更新图表配置（下探后）
   */
  updateChartConfig(data: ChartDataItem[], targetLevel: number, chartConfig: ChartConfig, parentData: ChartDataItem): Partial<ChartConfig> {
    try {
      const configManager = getClueSourceChartConfigManager();

      const configUpdate = configManager.updateChartData(
        chartConfig,
        [], // 空的MonthlyChannelData，因为我们已经有转换后的数据
        targetLevel,
        data,
        parentData
      );

      console.log('🎯 线索来源配置更新生成:', configUpdate);
      return configUpdate;
    } catch (error) {
      console.error('配置更新失败，使用默认配置:', error);
      // 回退到基类默认实现
      return {
        customProps: {
          ...chartConfig.customProps,
          drillDownLevel: targetLevel,
          drillDownData: data,
          parentData,
          isDrillDown: true,
          loading: false,
        },
      };
    }
  }

  /**
   * 重置到顶层的配置
   */
  async resetToTopLevelConfig(chartConfig: ChartConfig): Promise<Partial<ChartConfig>> {
    // 使用线索来源配置管理器生成顶层配置
    return await this.generateClueSourceTopLevelConfig(chartConfig);
  }
  /**
   * 生成线索来源顶层配置
   */
  private async generateClueSourceTopLevelConfig(chartConfig: ChartConfig): Promise<Partial<ChartConfig>> {
    const configManager = getClueSourceChartConfigManager();

    const currentDataSource = chartConfig.customProps?.currentDataSource || chartConfig.dataSource;
    const baseConfig = configManager.generateBaseConfig(currentDataSource as any, 0);

    const topLevelConfig = {
      title: baseConfig.title,
      options: baseConfig.options,
      customProps: {
        ...baseConfig.customProps,
        currentDataSource,
        alternativeDataSource: configManager.getAlternativeInfo(currentDataSource).dataSource,
        alternativeTitle: configManager.getAlternativeInfo(currentDataSource).title,
        needsAsyncData: true,
        loading: true,
      },
    };

    console.log('🎯 生成的顶层配置:', topLevelConfig);
    return topLevelConfig;
  }
}

// ========== 策略工厂 ==========

/**
 * 下探策略工厂
 * 🔥 优化：继承 BaseSingletonStrategyFactory，使用统一的单例策略管理
 */
export class ChartDrillDownStrategyFactory extends BaseSingletonStrategyFactory<IChartDrillDownStrategy, ChartConfig> {
  private constructor() {
    super('ChartDrillDownStrategyFactory');
  }

  /**
   * 获取工厂单例实例
   */
  public static getInstance(): ChartDrillDownStrategyFactory {
    return this.getOrCreateInstance('ChartDrillDownStrategyFactory', () => new ChartDrillDownStrategyFactory());
  }

  /**
   * 获取策略 - 实现基类的抽象方法
   */
  getStrategy(chartConfig: ChartConfig): IChartDrillDownStrategy | null {
    console.log(`🔍 查找下探策略 - 图表ID: ${chartConfig.id}, 图表类型: ${chartConfig.type}`);

    // 1. 优先使用配置中明确指定的策略
    const specifiedStrategy = chartConfig.customProps?.drillDownStrategy;
    if (specifiedStrategy) {
      const strategy = this.getStrategy(specifiedStrategy) as IChartDrillDownStrategy;
      if (strategy) {
        console.log(`🎯 使用指定下探策略: ${specifiedStrategy}`);
        return strategy;
      }
    }

    // 2. 根据图表ID自动匹配
    const strategies = this.getAllStrategies() as IChartDrillDownStrategy[];
    for (const strategy of strategies) {
      if (this.matchByChartId(chartConfig.id, strategy.strategyType)) {
        console.log(`🎯 根据图表ID匹配下探策略: ${strategy.strategyType} for ${chartConfig.id}`);
        return strategy;
      }
    }

    // 3. 根据图表类型和下探配置匹配
    for (const strategy of strategies) {
      if (strategy.supportedChartTypes.includes(chartConfig.type) && chartConfig.drillDown?.enabled) {
        console.log(`🎯 根据图表类型匹配下探策略: ${strategy.strategyType} for ${chartConfig.type}`);
        return strategy;
      }
    }

    console.warn(`⚠️ 未找到合适的下探策略: ${chartConfig.id}, 类型: ${chartConfig.type}`);
    return null;
  }

  /**
   * 根据策略类型获取策略
   */
  getStrategyByType(strategyType: string): IChartDrillDownStrategy | null {
    return super.getStrategyByType(strategyType);
  }

  /**
   * 实例方法：注册策略（重写基类方法）
   */
  registerStrategy(strategy: IChartDrillDownStrategy): void {
    super.registerStrategy(strategy);
  }

  /**
   * 根据图表ID匹配策略类型
   */
  private matchByChartId(chartId: string, strategyType: string): boolean {
    const mappings: Record<string, string[]> = {
      'clue-source-drill': ['sourceOfClues', 'clueSource'],
      // 未来可以添加更多映射...
    };

    const chartIds = mappings[strategyType] || [];
    return chartIds.some((id) => chartId.includes(id));
  }

  /**
   * 静态方法：注册策略
   */
  static registerStrategy(strategy: IChartDrillDownStrategy): void {
    ChartDrillDownStrategyFactory.getInstance().registerStrategy(strategy);
    console.log(`📝 注册下探策略: ${strategy.strategyType}`);
  }

  /**
   * 静态方法：获取所有已注册的策略
   */
  static getAllStrategies(): IChartDrillDownStrategy[] {
    return ChartDrillDownStrategyFactory.getInstance().getAllStrategies();
  }

  /**
   * 静态方法：清除所有策略（用于测试）
   */
  static clearStrategies(): void {
    ChartDrillDownStrategyFactory.getInstance().clearStrategies();
  }
}

// ========== 初始化策略注册 ==========

// 🔥 优化：使用依赖注入自动注册线索来源下探策略
const clueSourceStrategy = createChartDrillDownStrategy(ClueSourceDrillDownStrategy);
console.log(`📝 正在注册策略: ${clueSourceStrategy.strategyType}, 支持类型: [${clueSourceStrategy.supportedChartTypes.join(', ')}]`);
ChartDrillDownStrategyFactory.registerStrategy(clueSourceStrategy);

// 验证注册成功
const registeredStrategies = ChartDrillDownStrategyFactory.getAllStrategies();
console.log(
  `✅ 下探策略系统初始化完成（使用依赖注入） - 已注册 ${registeredStrategies.length} 个策略: [${registeredStrategies.map((s) => s.strategyType).join(', ')}]`
);
