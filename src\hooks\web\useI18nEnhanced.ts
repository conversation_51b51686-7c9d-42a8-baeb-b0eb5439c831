/**
 * 增强版国际化Hook
 * 在保持现有useI18n逻辑完全兼容的基础上，增加多模块支持
 *
 * 使用方式：
 * 1. 现有方式（完全兼容）：useI18n() 或 useI18n('common')
 * 2. 新的模块方式：useI18n('clue') 或 useI18n('workOrder')
 * 3. 动态加载模块：useI18nWithModule('clue-vue')
 */

import { MULTILINGUAT_KEY, LOCALE_KEY } from '/@/enums/cacheEnum';
import { localeSetting } from '/@/settings/localeSetting';
import { createLocalStorage } from '/@/utils/cache';
import { multiModuleI18nManager, loadI18nModule } from '/@/utils/i18n/multiModuleI18nManager';
import type { multiLingualResourceType } from '/@/api/common/api';
import { ref } from 'vue';

const ls = createLocalStorage();

// 语言存储数据 - 使用响应式引用以支持动态更新
const languageParams = ref({});

/**
 * 获取语言数据
 */
function getLanguage() {
  const languageStr = localStorage.getItem(MULTILINGUAT_KEY);
  const parsed = languageStr ? JSON.parse(languageStr) : {};
  languageParams.value = parsed;
  return parsed;
}

// 初始化语言数据
getLanguage();

// 监听资源更新事件
if (typeof window !== 'undefined') {
  window.addEventListener('i18n-resources-updated', () => {
    getLanguage();
  });
}

// 存储的当前语言
const languageType = (ls.get(LOCALE_KEY) || localeSetting)?.locale || 'en-US';

export type I18nGlobalTranslation = {
  (key: string): string;
  (key: string, locale: string): string;
  (key: string, locale: string, list: unknown[]): string;
  (key: string, locale: string, named: Record<string, unknown>): string;
  (key: string, list: unknown[]): string;
  (key: string, named: Record<string, unknown>): string;
};

/**
 * 增强版国际化Hook - 完全兼容现有逻辑
 * @param namespace 命名空间，默认为空字符串（保持现有行为）
 * @param language 指定语言
 */
function useI18nEnhanced(namespace = '', language?: string) {
  const tFn: I18nGlobalTranslation = (key: string, ...arg: any[]) => {
    if (!key) return '';
    if (!key.includes('.') && !namespace) return key;

    if (Object.keys(languageParams).length === 0) {
      getLanguage();
    }
    const currentLanguageParams = languageParams.value;
    // 处理多层级 key 结构：namespace.xxx.xxx.xxx
    const fullKey = !!namespace ? `${namespace}.${key}` : key;

    // 首先尝试直接访问完整key（不分解）
    let result = null;

    // 如果有namespace，先尝试访问namespace下的完整key
    if (namespace && currentLanguageParams[namespace] && currentLanguageParams[namespace][key]) {
      result = currentLanguageParams[namespace][key];
    }

    // 获取最终的翻译文本
    const targetLanguage = language || languageType;
    let str = result?.[targetLanguage] || key;

    if (str === key && namespace) {
      console.warn(`⚠️ 翻译失败: ${fullKey} -> ${targetLanguage}`);
    }

    // 参数替换逻辑（保持原有逻辑）
    if (arg?.length > 0) {
      for (const i in arg) {
        for (const j in arg[i]) {
          str = str.replace(`{${j}}`, arg[i][j]);
        }
      }
    }

    return str;
  };

  return {
    t: tFn,
  };
}

/**
 * 带模块自动加载的国际化Hook
 * 会自动加载指定模块的多语言资源，然后返回对应命名空间的翻译函数
 * @param resourceType 资源类型
 * @param language 指定语言
 */
export function useI18nWithModule(resourceType: multiLingualResourceType, language?: string) {
  const namespace = multiModuleI18nManager.getModuleNamespace(resourceType);
  const isLoaded = ref(multiModuleI18nManager.isModuleLoaded(resourceType));
  const loading = ref(false);
  const error = ref<Error | null>(null);

  // 创建翻译函数
  const { t } = useI18nEnhanced(namespace, language);

  /**
   * 加载模块资源
   */
  const loadModule = async (forceReload = false) => {
    if (loading.value && !forceReload) return;

    try {
      loading.value = true;
      error.value = null;

      console.log(`🔄 开始加载模块 ${resourceType} 的多语言资源...`);
      await loadI18nModule(resourceType);

      // 加载完成后，强制刷新语言数据并更新状态
      getLanguage();
      isLoaded.value = multiModuleI18nManager.isModuleLoaded(resourceType);

      console.log(`✅ 模块 ${resourceType} 加载完成，状态: ${isLoaded.value}`);
    } catch (err) {
      error.value = err as Error;
      console.error(`❌ 加载模块 ${resourceType} 失败:`, err);
    } finally {
      loading.value = false;
    }
  };

  // 检查模块是否已加载，只在未加载时才请求接口
  if (!loading.value && !multiModuleI18nManager.isModuleLoaded(resourceType)) {
    console.log(`📦 模块 ${resourceType} 未加载，开始加载...`);
    loadModule();
  } else if (multiModuleI18nManager.isModuleLoaded(resourceType)) {
    console.log(`✅ 模块 ${resourceType} 已加载，跳过接口请求`);
    isLoaded.value = true;
    // 确保语言数据是最新的
    getLanguage();
  }

  return {
    t,
    isLoaded,
    loading,
    error,
    loadModule,
    namespace,
    // 便捷方法：强制重新加载
    reloadModule: () => loadModule(true),
  };
}

/**
 * 批量加载多个模块的Hook
 * @param resourceTypes 资源类型数组
 * @param language 指定语言
 */
export function useI18nWithModules(resourceTypes: multiLingualResourceType[], language?: string) {
  const loading = ref(false);
  const error = ref<Error | null>(null);
  const loadedModules = ref<string[]>([]);

  // 为每个模块创建翻译函数
  const translators = resourceTypes.reduce(
    (acc, resourceType) => {
      const namespace = multiModuleI18nManager.getModuleNamespace(resourceType);
      acc[resourceType] = useI18nEnhanced(namespace, language).t;
      return acc;
    },
    {} as Record<multiLingualResourceType, I18nGlobalTranslation>
  );

  /**
   * 批量加载所有模块
   */
  const loadAllModules = async () => {
    if (loading.value) return;

    try {
      loading.value = true;
      error.value = null;

      await Promise.all(
        resourceTypes.map(async (resourceType) => {
          await loadI18nModule(resourceType);
          if (!loadedModules.value.includes(resourceType)) {
            loadedModules.value.push(resourceType);
          }
        })
      );
    } catch (err) {
      error.value = err as Error;
      console.error('批量加载模块失败:', err);
    } finally {
      loading.value = false;
    }
  };

  // 检查哪些模块已加载
  const updateLoadedStatus = () => {
    loadedModules.value = resourceTypes.filter((type) => multiModuleI18nManager.isModuleLoaded(type));
  };

  updateLoadedStatus();

  return {
    translators,
    loading,
    error,
    loadedModules,
    loadAllModules,
    updateLoadedStatus,
  };
}

/**
 * 监听多语言资源变化的Hook
 */
export function useI18nResourceWatcher() {
  const loadedModules = ref(multiModuleI18nManager.getLoadedModules());

  // 监听资源更新
  const updateLoadedModules = () => {
    loadedModules.value = multiModuleI18nManager.getLoadedModules();
  };

  if (typeof window !== 'undefined') {
    window.addEventListener('i18n-resources-updated', updateLoadedModules);
  }

  return {
    loadedModules,
    updateLoadedModules,
  };
}

// 为了保持向后兼容，重新导出原有的t函数
export const t = (key: string) => key;
