import { computed, reactive } from 'vue';

/**
 * 局部查询参数管理 Hook
 * 用于管理特定 group 的局部筛选器参数状态
 */
export interface LocalQueryParamsState {
  /** 局部参数 */
  localParams: Record<string, any>;
  /** 是否已初始化 */
  initialized: boolean;
  /** 初始化 Promise */
  initializationPromise?: Promise<void>;
  /** 初始化 Promise 的 resolve 函数 */
  initializationResolve?: () => void;
}

/**
 * 局部查询参数管理器
 */
class LocalQueryParamsManager {
  private groupStates = new Map<string, LocalQueryParamsState>();

  /**
   * 获取或创建 group 的状态
   */
  getGroupState(groupId: string): LocalQueryParamsState {
    if (!this.groupStates.has(groupId)) {
      let initializationResolve: (() => void) | undefined;
      const initializationPromise = new Promise<void>((resolve) => {
        initializationResolve = resolve;
      });
      this.groupStates.set(
        groupId,
        reactive({
          localParams: {},
          initialized: false,
          initializationPromise,
          initializationResolve,
        })
      );
    }
    return this.groupStates.get(groupId)!;
  }

  /**
   * 更新 group 的局部参数
   */
  updateGroupParams(groupId: string, params: Record<string, any>) {
    const state = this.getGroupState(groupId);
    Object.assign(state.localParams, params);
  }

  /**
   * 重置 group 的局部参数
   */
  resetGroupParams(groupId: string) {
    const state = this.getGroupState(groupId);
    state.localParams = {};
    state.initialized = false;
  }

  /**
   * 获取 group 的局部参数
   */
  getGroupParams(groupId: string): Record<string, any> {
    const state = this.getGroupState(groupId);
    return { ...state.localParams };
  }

  /**
   * 标记 group 已初始化
   */
  markGroupInitialized(groupId: string) {
    const state = this.getGroupState(groupId);
    state.initialized = true;
    // 触发初始化完成的 Promise
    if (state.initializationResolve) {
      state.initializationResolve();
      state.initializationResolve = undefined;
    }
  }

  /**
   * 检查 group 是否已初始化
   */
  isGroupInitialized(groupId: string): boolean {
    const state = this.getGroupState(groupId);
    return state.initialized;
  }

  /**
   * 清理所有状态
   */
  clear() {
    this.groupStates.clear();
  }
}

// 全局管理器实例
const localQueryParamsManager = new LocalQueryParamsManager();

/**
 * 局部查询参数 Hook
 * @param groupId - 组 ID
 * @param config - 局部筛选器配置
 */
export function useLocalQueryParams(groupId: string) {
  // 获取当前 group 的状态
  const groupState = localQueryParamsManager.getGroupState(groupId);

  // 响应式的局部参数
  const localParams = computed(() => groupState.localParams);

  // 是否已初始化
  const initialized = computed(() => groupState.initialized);

  /**
   * 更新局部参数
   */
  const updateLocalParams = (params: Record<string, any>) => {
    localQueryParamsManager.updateGroupParams(groupId, params);
  };

  /**
   * 设置局部参数（完全替换）
   */
  const setLocalParams = (params: Record<string, any>) => {
    const state = localQueryParamsManager.getGroupState(groupId);
    state.localParams = { ...params };
  };

  /**
   * 初始化局部参数
   */
  const initializeLocalParams = (defaultParams?: Record<string, any>) => {
    if (!localQueryParamsManager.isGroupInitialized(groupId)) {
      const initialParams = {
        ...defaultParams,
      };
      setLocalParams(initialParams);
      localQueryParamsManager.markGroupInitialized(groupId);
      console.log('initializeLocalParams -> initialParams', initialParams);
    }
  };

  /**
   * 获取初始化 Promise
   */
  const getInitializationPromise = () => {
    return groupState.initializationPromise;
  };

  return {
    /** 响应式的局部参数 */
    localParams,
    /** 是否已初始化 */
    initialized,
    /** 更新局部参数 */
    updateLocalParams,
    /** 设置局部参数（完全替换） */
    setLocalParams,
    /** 初始化局部参数 */
    initializeLocalParams,
    /** 获取初始化 Promise */
    getInitializationPromise,
  };
}

/**
 * 全局局部参数管理工具
 */
export const localQueryParamsUtils = {
  /** 获取指定 group 的参数 */
  getGroupParams: (groupId: string) => localQueryParamsManager.getGroupParams(groupId),
  /** 更新指定 group 的参数 */
  updateGroupParams: (groupId: string, params: Record<string, any>) => localQueryParamsManager.updateGroupParams(groupId, params),
  /** 重置指定 group 的参数 */
  resetGroupParams: (groupId: string) => localQueryParamsManager.resetGroupParams(groupId),
  /** 清理所有状态 */
  clear: () => localQueryParamsManager.clear(),
};
