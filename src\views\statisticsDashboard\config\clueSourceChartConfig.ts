import { useI18n } from '/@/hooks/web/useI18n';
import type { ChartConfig, ChartDataItem, MonthlyChannelData, ChannelConfig, DrillDownConfig } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter, formatValueWithPercentage, generateChartDataItem } from '../utils';

const { t } = useI18n('common');

export const clueSourceDrillConfig: DrillDownConfig = {
  enabled: true,
  currentLevel: 0,
  maxLevel: 1,
  dataStrategy: 'async',
  levels: [
    {
      level: 0,
      dataKey: 'channelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'channelKey',
    },
    {
      level: 1,
      dataKey: 'subChannelKey',
      titleField: 'name',
      valueField: 'value',
      colorField: 'subChannelKey',
      parentKey: 'channelKey',
    },
  ],
};

export const clueSourceChannelConfig: ChannelConfig[] = [
  {
    key: 'onlinePublic',
    name: t('onlinePublicDomain'),
    color: '#5470c6', // 蓝色
    children: [
      { key: 'social', name: '社媒', color: '#9c27b0' },
      { key: 'vertical', name: '垂媒', color: '#607d8b' },
    ],
  },
  {
    key: 'onlinePrivate',
    name: t('onlinePrivateDomain'),
    color: '#91cc75', // 绿色
    children: [
      { key: 'app', name: 'APP', color: '#9c27b0' },
      { key: 'website', name: t('officeWeb'), color: '#e91e63' },
      { key: 'serviceCenter', name: t('customerServiceCenter'), color: '#00bcd4' },
    ],
  },
  {
    key: 'offlinePrivate',
    name: t('offlinePrivateDomain'),
    color: '#fac858', // 黄色
    children: [
      { key: 'marketing', name: '营销活动', color: '#ff9800' },
      { key: 'carshow', name: '车展', color: '#795548' },
    ],
  },
];

export const formatTooltip = createTooltipFormatter({ showPercentage: true });

export const findChannelConfig = (channelKey: string): ChannelConfig | null => {
  for (const config of clueSourceChannelConfig) {
    if (config.key === channelKey) {
      return config;
    }
    if (config.children) {
      for (const child of config.children) {
        if (child.key === channelKey) {
          return child;
        }
      }
    }
  }
  return null;
};

export class ClueSourceChartConfigManager {
  private static instance: ClueSourceChartConfigManager;

  static getInstance(): ClueSourceChartConfigManager {
    if (!ClueSourceChartConfigManager.instance) {
      ClueSourceChartConfigManager.instance = new ClueSourceChartConfigManager();
    }
    return ClueSourceChartConfigManager.instance;
  }

  generateBaseConfig(dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues', drillDownLevel: number = 0): ChartConfig {
    const isAllClues = dataSource === 'sourceOfAllClues';
    const title = drillDownLevel === 1 ? t('channelLeadAnalysis') : isAllClues ? t('sourceOfAllClues') : t('effectiveSourcesOfClues');

    return {
      id: 'sourceOfClues',
      type: 'bar',
      title,
      dataSource,
      drillDown: clueSourceDrillConfig,
      loadingStrategy: 'source-of-clues',
      customProps: {
        switchable: true,
        currentDataSource: dataSource,
        alternativeDataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
        alternativeTitle: isAllClues ? t('effectiveSourcesOfClues') : t('sourceOfAllClues'),
        drillDownLevel,
        needsAsyncData: true,
        loading: true,
      },
      options: {
        title: { show: false },
        color: clueSourceChannelConfig.map((config) => config.color),
        grid: {
          left: '4%',
          right: '4%',
          bottom: '15%',
          containLabel: true,
        },
        legend: {
          data: clueSourceChannelConfig.map((config) => config.name),
          bottom: '5%',
          left: 'center',
        },
        xAxis: { type: 'category', data: [], axisLabel: { interval: 0, rotate: 30 } },
        yAxis: { type: 'value', name: t('numberOfClues') },
        series: clueSourceChannelConfig.map((channelConfig) => ({
          name: channelConfig.name,
          type: 'bar',
          data: [],
          itemStyle: { color: channelConfig.color },
          label: {
            show: true,
            position: 'top',
            formatter: (params: any) => formatValueWithPercentage(params.data.value, params.data.percent),
          },
        })),
        tooltip: { show: true, trigger: 'axis', axisPointer: { type: 'shadow' }, formatter: formatTooltip },
      },
      size: { height: 450 },
      position: { x: 0, y: 0 },
    };
  }

  updateChartData(
    baseConfig: ChartConfig,
    data: MonthlyChannelData[],
    drillDownLevel: number = 0,
    drillDownData?: ChartDataItem[],
    parentData?: ChartDataItem
  ): Partial<ChartConfig> {
    return drillDownLevel === 1
      ? this.generateSecondLevelUpdate(baseConfig, drillDownData, parentData)
      : this.generateFirstLevelUpdate(baseConfig, data);
  }

  private generateFirstLevelUpdate(baseConfig: ChartConfig, data: MonthlyChannelData[]): Partial<ChartConfig> {
    const series = clueSourceChannelConfig.map((channelConfig) => ({
      name: channelConfig.name,
      type: 'bar',
      data: data?.map((item) => {
        const value = item.channels[channelConfig.key] || 0;
        const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
        return generateChartDataItem(item.name, value, percent, { channelKey: channelConfig.key });
      }),
      itemStyle: { color: channelConfig.color },
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => formatValueWithPercentage(params.data.value, params.data.percent),
      },
    }));

    return {
      customProps: { ...baseConfig.customProps, loading: false, drillDownLevel: 0 },
      options: {
        ...baseConfig.options,
        xAxis: { ...baseConfig.options?.xAxis, type: 'category', data: data?.map((item) => item.name) ?? [] },
        series: series as any,
      },
    };
  }

  private generateSecondLevelUpdate(baseConfig: ChartConfig, drillDownData?: ChartDataItem[], parentData?: ChartDataItem): Partial<ChartConfig> {
    const actualDrillDownData = drillDownData || [];

    if (!parentData) {
      return {
        title: t('channelLeadAnalysis'),
        customProps: { ...baseConfig.customProps, loading: false, drillDownLevel: 1 },
      };
    }

    const parentConfig = findChannelConfig(parentData.channelKey);

    if (!parentConfig?.children) {
      return {
        customProps: { ...baseConfig.customProps, loading: false, drillDownLevel: 1 },
      };
    }

    const groupedData: Record<string, ChartDataItem[]> = {};
    parentConfig.children.forEach((childConfig) => {
      groupedData[childConfig.key] = actualDrillDownData.filter((item) => item.channelKey === childConfig.key);
    });
    const months = Array.from(new Set(actualDrillDownData.map((item) => item.month))).sort();

    const series = parentConfig.children.map((childConfig) => ({
      name: childConfig.name,
      type: 'bar',
      data:
        groupedData[childConfig.key]?.map((item) => {
          const monthlyTotal = actualDrillDownData
            .filter((dataItem) => dataItem.month === item.month)
            .reduce((sum, dataItem) => sum + (Number(dataItem.value) || 0), 0);
          const itemValue = Number(item.value) || 0;
          const percent = monthlyTotal > 0 ? calculatePercentage(itemValue, monthlyTotal) : '0.0';
          return generateChartDataItem(item.month, itemValue, percent, { channelKey: childConfig.key });
        }) || [],
      itemStyle: { color: childConfig.color },
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => formatValueWithPercentage(params.data.value, params.data.percent),
      },
    }));

    return {
      title: t('channelLeadAnalysis'),
      customProps: { ...baseConfig.customProps, loading: false, drillDownLevel: 1 },
      options: {
        ...baseConfig.options,
        color: parentConfig.children.map((child) => child.color),
        legend: { data: parentConfig.children.map((child) => child.name), bottom: '5%' },
        xAxis: { ...baseConfig.options?.xAxis, type: 'category', data: months },
        series: series as any,
      },
    };
  }

  getChannelConfig(): ChannelConfig[] {
    return clueSourceChannelConfig;
  }

  static getTitleByDataSource(dataSource: string, drillDownLevel: number = 0): string {
    if (drillDownLevel === 1) return t('channelLeadAnalysis');
    const isAllClues = dataSource === 'sourceOfAllClues';
    return isAllClues ? t('sourceOfAllClues') : t('effectiveSourcesOfClues');
  }

  getAlternativeInfo(currentDataSource: string): { dataSource: string; title: string } {
    const isAllClues = currentDataSource === 'sourceOfAllClues';
    return {
      dataSource: isAllClues ? 'sourceOfEffectiveClues' : 'sourceOfAllClues',
      title: isAllClues ? t('effectiveSourcesOfClues') : t('sourceOfAllClues'),
    };
  }
}

export const getClueSourceChartConfigManager = () => ClueSourceChartConfigManager.getInstance();

export const generateBaseClueSourceChartConfig = (
  dataSource: 'sourceOfAllClues' | 'sourceOfEffectiveClues' = 'sourceOfAllClues',
  drillDownLevel: number = 0
): ChartConfig => getClueSourceChartConfigManager().generateBaseConfig(dataSource, drillDownLevel);

export const updateClueSourceChartData = (
  baseConfig: ChartConfig,
  data: MonthlyChannelData[],
  drillDownLevel: number = 0,
  drillDownData?: ChartDataItem[],
  parentData?: ChartDataItem,
  isEmpty: boolean = false
): Partial<ChartConfig> => {
  const result = getClueSourceChartConfigManager().updateChartData(baseConfig, data, drillDownLevel, drillDownData, parentData);
  return { ...result, customProps: { ...result.customProps, isEmpty } };
};

export const sourceOfCluesChartConfig: ChartConfig = generateBaseClueSourceChartConfig('sourceOfAllClues');
