# 统计看板统一架构指南

## 📋 概述

统计看板采用现代化的Vue 3架构，通过**统一查询参数管理器 + 依赖注入容器 + 策略模式 + 数据源管理器**实现了完全的组件解耦和统一数据管理。

**核心特点**：
- 🎯 **零透传架构**：0层数据传递，0个事件透传，0个组件暴露
- 🏗️ **统一参数管理**：全局和局部参数的统一管理机制，支持参数作用域隔离
- 🔧 **依赖注入模式**：策略类通过依赖容器获取查询参数管理器，确保参数可用性
- 📊 **策略模式**：自动选择合适的数据加载策略，支持扩展
- 🗄️ **统一数据源管理**：缓存、元数据、类型安全的数据管理
- 🛡️ **职责分离**：装饰器专注错误处理，策略专注业务逻辑
- ⚡ **防抖机制**：参数变化防抖处理，避免重复调用
- ✨ **内置状态管理**：操作上下文内置状态管理，无需单独状态上下文

## 🏗️ 整体架构

### 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                    统计看板主组件                              │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │ Provide 上下文
┌─────────────────────▼───────────────────────────────────────┐
│                   上下文系统层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ChartActions    │  │StatisticsActions│  │UnifiedQuery  │ │
│  │ (内置状态管理)   │  │ (内置状态管理)   │  │ParamsManager │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 依赖注入
┌─────────────────────▼───────────────────────────────────────┐
│                 策略依赖注入容器                              │
│              StrategyDependencyContainer                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  查询参数管理器注入 + 数据源管理器注入                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 统一数据管理
┌─────────────────────▼───────────────────────────────────────┐
│                  数据源管理器                                 │
│              useDataSourceManager                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  统一数据源映射 (支持图表和统计数据)                        │ │
│  │  - 缓存管理    - 元数据管理    - 类型安全                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 策略选择
┌─────────────────────▼───────────────────────────────────────┐
│                   策略工厂层                                  │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartDataLoading │              │StatisticsDataLoading    │ │
│  │StrategyFactory  │              │StrategyFactory          │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 具体策略实现
┌─────────────────────▼───────────────────────────────────────┐
│                   策略实现层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ClueSourceChart  │  │ClueOverview     │  │DefaultMock   │ │
│  │Strategy         │  │Statistics       │  │Strategy      │ │
│  │                 │  │Strategy         │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ API装饰器自动注入参数
┌─────────────────────▼───────────────────────────────────────┐
│                    API调用层                                 │
│              ApiCallDecorator                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  自动注入筛选参数 → API调用 → 数据转换 → 缓存存储          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心系统详解

### 1. 上下文系统 (Context System)

#### 设计原则
- **内置状态管理**：操作上下文内置状态管理，避免注入顺序问题
- **统一架构**：图表和统计数据使用相同的上下文模式
- **类型安全**：完整的TypeScript类型定义

#### 图表操作上下文
```typescript
interface ChartActionContext {
  // 核心操作
  switchChartDataSource: (chartId: string, newDataSource: string, newTitle: string) => void;
  refreshChart: (chartId: string) => void;
  exportChart: (chartId: string, format: string) => void;
  fullscreenChart: (chartId: string) => void;
  
  // 下探功能
  handleDrillDown?: (data: ChartDataItem, chartConfig: any) => Promise<void>;
  resetChartToTopLevel?: (chartConfig: any) => Promise<void>;
  
  // 内置状态管理
  chartLoadingStates: Record<string, boolean>;
  chartErrorStates: Record<string, string | null>;
  setChartLoading: (chartId: string, loading: boolean) => void;
  setChartError: (chartId: string, error: string | null) => void;
}
```

#### 统计数据操作上下文
```typescript
interface StatisticsActionContext {
  // 核心操作
  refreshStatistics: (configId: string) => Promise<void>;
  reloadAllStatistics: () => Promise<void>;
  
  // 内置状态管理
  statisticsLoadingStates: Record<string, boolean>;
  statisticsErrorStates: Record<string, string | null>;
  setStatisticsLoading: (configId: string, loading: boolean) => void;
  setStatisticsError: (configId: string, error: string | null) => void;
}
```

### 2. 数据源管理器 (Data Source Manager)

#### 核心功能
- **统一存储**：图表和统计数据使用同一个数据源映射
- **类型安全**：通过泛型支持不同数据类型
- **元数据管理**：完整的数据源元信息管理
- **缓存机制**：自动缓存和更新数据
- **🔥 全局单例**：确保整个应用共享同一个数据源管理器实例

#### 🚀 架构优化：全局单例模式

**问题背景**：
- 原有架构中每次调用 `useDataSourceManager()` 都会创建新实例
- `BaseStrategy` 和 `useChartActions` 都独立实例化数据源管理器
- 导致数据不同步、资源浪费和状态管理混乱

**解决方案**：
- 实现全局单例 `DataSourceManager`
- 通过依赖注入统一数据源管理
- 确保所有组件和策略共享同一个数据源实例

#### 使用示例
```typescript
// 使用全局单例数据源管理器
const dataSourceManager = useGlobalDataSourceManager();

// 存储图表数据
dataSourceManager.setDataSource<ChartDataItem>('sourceOfClues', chartData, {
  name: '线索来源',
  type: 'chart',
  dataType: 'ChartDataItem'
});

// 存储统计数据
dataSourceManager.setDataSource<StatisticsItem>('clueOverview', statsData, {
  name: '线索总览',
  type: 'statistics', 
  dataType: 'StatisticsItem'
});

// 获取数据（类型安全）
const chartData = dataSourceManager.getDataSource<ChartDataItem>('sourceOfClues');
const statsData = dataSourceManager.getDataSource<StatisticsItem>('clueOverview');
```

#### 接口定义
```typescript
export interface IDataSourceManager {
  // 数据访问
  getDataSource: <T = ChartDataItem>(dataSourceKey: string) => T[];
  setDataSource: <T = ChartDataItem>(dataSourceKey: string, data: T[], meta?: Partial<DataSourceMeta>) => void;
  updateDataSource: (dataSourceKey: string, data: ChartDataItem[]) => void;
  removeDataSource: (dataSourceKey: string) => void;
  
  // 元数据管理
  getDataSourceMeta: (dataSourceKey: string) => DataSourceMeta | undefined;
  getAllDataSourceKeys: () => string[];
  getDataSourcesByCategory: <T = ChartDataItem>(category: string) => Record<string, T[]>;
  
  // 管理方法
  clearAllDataSources: () => void;
  resetToDefaults: () => void;
  
  // 只读属性
  readonly dataSourceMap: Readonly<Record<string, ChartDataItem[] | StatisticsItem[]>>;
  readonly dataSourceMeta: Readonly<Record<string, DataSourceMeta>>;
  readonly dataSourceStats: {
    totalDataSources: number;
    totalCategories: number;
    totalDataPoints: number;
    categories: string[];
  };
  readonly dataSourcesByCategory: Record<string, string[]>;
}
```

#### 迁移指南

**1. 更新导入语句**
```typescript
import { useGlobalDataSourceManager } from '../hooks/useDataSourceManager';
// 或者
import useGlobalDataSourceManager from '../hooks/useDataSourceManager';
```

**2. 更新使用方式**
```typescript

const dataSourceManager = useGlobalDataSourceManager();
```

**3. 策略类构造函数支持依赖注入**
```typescript
// BaseStrategy 现在支持依赖注入
export abstract class BaseStrategy {
  protected dataSourceManager: IDataSourceManager;
  
  constructor(dataSourceManager?: IDataSourceManager) {
    this.dataSourceManager = dataSourceManager || useGlobalDataSourceManager();
  }
}
```

#### 优化成果

- **🎯 数据一致性**：所有组件和策略共享同一个数据源实例
- **🚀 性能提升**：避免重复实例化，减少内存占用
- **🔧 易于维护**：统一的数据源管理，简化调试和维护
- **📦 向后兼容**：保留原有API，平滑迁移
- **🛡️ 类型安全**：完整的TypeScript类型支持

### 3. 策略模式 (Strategy Pattern)

#### 图表数据加载策略
```typescript
// 抽象基类
export abstract class BaseChartDataLoadingStrategy {
  protected apiDecorator: IApiCallDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  // 模板方法：标准数据加载流程
  async loadData(chartConfig: ChartConfig, customParams = {}) {
    // 1. 验证配置
    // 2. 设置加载状态  
    // 3. 通过装饰器调用API
    // 4. 数据转换
    // 5. 存储到数据源管理器
    // 6. 返回结果
  }
  
  // 🔥 新增：图表配置更新方法
  abstract updateChartConfig(
    chartConfig: ChartConfig, 
    data: ChartDataItem[], 
    newDataSource?: string
  ): Promise<Partial<ChartConfig>>;
  
  // 通用配置增强方法
  protected enhanceUpdateConfig(
    updateConfig: Partial<ChartConfig>,
    data: ChartDataItem[],
    newDataSource?: string
  ): Partial<ChartConfig> {
    return {
      ...updateConfig,
      customProps: {
        ...updateConfig.customProps,
        loading: false,
        isEmpty: !data || data.length === 0,
        ...(newDataSource && { currentDataSource: newDataSource })
      }
    };
  }
}

// 具体策略实现
export class ClueSourceChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'clue-source';
  readonly supportedChartTypes = ['bar', 'column'];
  
  protected async fetchApiData(params: commonQueryParams) {
    const { queryAllClueSource } = await import('../api');
    return await queryAllClueSource(params);
  }
  
  // 实现配置更新逻辑
  async updateChartConfig(
    chartConfig: ChartConfig, 
    data: ChartDataItem[], 
    newDataSource?: string
  ): Promise<Partial<ChartConfig>> {
    const { updateClueSourceChartData } = await import('../config/clueSourceChartConfig');
    
    const updateConfig = updateClueSourceChartData(
      chartConfig,
      data as any,
      chartConfig.customProps?.drillDownLevel || 0,
      chartConfig.customProps?.drillDownData,
      chartConfig.customProps?.parentData,
      !data || data.length === 0
    );
    
    return this.enhanceUpdateConfig(updateConfig, data, newDataSource);
  }
}
```

#### 图表下探策略统一
```typescript
// 下探策略基类
export abstract class BaseChartDrillDownStrategy {
  protected apiDecorator: IApiCallDecorator;
  
  // 统一下探数据获取流程
  async fetchRawDrillDownData(level: number, dataSource: string, params: any) {
    // 1. 根据层级和数据源调用对应API
    // 2. 使用现有数据转换工具
    // 3. 调用统一的月度数据转换方法
  }
  
  // 统一的月度数据转换
  abstract convertMonthlyDataToDrillDownData(data: any[], parentChannelKey?: string): any[];
}

// 线索来源下探策略
export class ClueSourceDrillDownStrategy extends BaseChartDrillDownStrategy {
  async fetchRawDrillDownData(level: number, dataSource: string, params: any) {
    // 调用二级线索来源API
    const apiResponse = await this.apiDecorator.decorateApiCall(
      level === 1 ? queryValidClueSourceSecond : queryAllClueSourceSecond,
      params
    );
    
    // 使用现有数据转换工具
    const transformedData = transformSecondApiDataToChartData(apiResponse);
    
    // 复用现有的月度数据转换方法
    return this.convertMonthlyDataToDrillDownData(transformedData, parentChannelKey);
  }
}
```

#### 数据转换工具模块 (dataTransform.ts)

**核心作用**：作为策略模式中的数据转换层，提供统一的数据处理能力

```typescript
// 主要功能模块
export const CLUE_SOURCE_SECOND_ID_TO_KEY_MAP: Record<string, string>;
export function transformSecondApiDataToChartData(apiResponse, parentSourceId): MonthlyChannelData[];
export function validateTransformedData(data);
```

**在策略模式中的职责**：
1. **数据映射标准化**：提供来源ID与内部key的双向映射关系
2. **API数据转换**：将原始API响应转换为图表组件标准格式
3. **数据验证**：确保转换后数据的完整性和正确性
4. **策略解耦**：让策略类专注业务逻辑，数据转换交给专门工具

**优势**：
- **复用性**：多个策略可共享相同的转换逻辑
- **维护性**：数据格式变更只需修改工具模块
- **测试性**：转换逻辑独立，便于单元测试
- **一致性**：保证所有策略的数据输出格式统一

#### 统计数据加载策略
```typescript
export abstract class BaseStatisticsDataLoadingStrategy {
  protected apiDecorator: IStatisticsApiDecorator;
  protected dataSourceManager: ReturnType<typeof useDataSourceManager>;
  
  async loadStatisticsData(config: StatisticsConfig, params?) {
    // 1. 使用装饰器自动注入参数
    // 2. 转换数据
    // 3. 存储到数据源管理器
    // 4. 返回结果
  }
}
```

### 4. API装饰器 (API Decorator)

#### 自动参数注入
```typescript
export class ApiCallDecorator implements IApiCallDecorator {
  async decorateApiCall<T>(apiCall: (params: commonQueryParams) => Promise<T>, customParams?) {
    // 🔥 自动获取当前筛选参数
    const currentParams = getCurrentQueryParams();
    
    // 合并参数：自定义参数 > 当前筛选参数
    const finalParams = { ...currentParams, ...customParams };
    
    // 调用API
    return await apiCall(finalParams);
  }
}
```

## 🔄 数据流程

### 上下文系统组件数据流转图

```mermaid
graph TB
    subgraph "主组件层"
        A[UserOperationStatisticsDashboard.vue]
    end

    subgraph "上下文提供层 (Provide)"
        B[ChartActions Context]
        C[StatisticsActions Context]
        D[ChartData Context]
        E[StatisticsData Context]
        F[QueryParams Context]
        G[ChartConfig Context]
    end

    subgraph "数据管理层"
        H[DataSourceManager<br/>统一数据源管理器]
        I[TabConfigManager<br/>配置管理器]
    end

    subgraph "策略执行层"
        J[ChartDataLoadingStrategy<br/>图表数据策略]
        K[StatisticsDataLoadingStrategy<br/>统计数据策略]
    end

    subgraph "组件消费层 (Inject)"
        L[ChartGroupContainer.vue]
        M[ChartGroup.vue]
        N[Individual Chart Components]
        O[Statistics Components]
    end

    subgraph "API调用层"
        P[API Decorator<br/>自动参数注入]
        Q[实际API调用]
    end

    %% 主组件提供上下文
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    A --> G

    %% 上下文依赖数据管理层
    B --> H
    B --> I
    C --> H
    C --> I
    D --> H
    E --> H
    G --> I

    %% 数据管理层调用策略
    H --> J
    H --> K

    %% 策略调用API
    J --> P
    K --> P
    P --> Q

    %% 组件注入上下文
    L -.-> B
    L -.-> C
    L -.-> D
    L -.-> E
    M -.-> B
    M -.-> D
    M -.-> G
    N -.-> B
    N -.-> D
    O -.-> C
    O -.-> E

    %% 筛选器更新流
    F --> P

    style A fill:#e1f5fe
    style H fill:#f3e5f5
    style P fill:#e8f5e8
    style B fill:#fff3e0
    style C fill:#fff3e0
```

### 详细数据流转说明

#### 1. 上下文提供阶段 (Provide Phase)
```
UserOperationStatisticsDashboard.vue
├── provideChartActions() → ChartActionContext
├── provideStatisticsActions() → StatisticsActionContext
├── provideChartData() → ChartDataContext
├── provideStatisticsData() → StatisticsDataContext
├── provideQueryParams() → QueryParamsContext
└── provideChartConfig() → ChartConfigContext
```

#### 2. 组件消费阶段 (Inject Phase)
```
ChartGroupContainer.vue
├── useStatisticsDataOptional() → 获取统计数据
├── useStatisticsActionsOptional() → 获取统计操作
└── 渲染统计卡片 + ChartGroup组件

ChartGroup.vue
├── useChartDataOptional() → 获取图表数据
├── useChartActionsOptional() → 获取图表操作
├── useChartConfigOptional() → 获取图表配置
└── 渲染具体图表组件

Individual Chart Components
├── useChartDataOptional() → 获取图表数据
├── useChartActionsOptional() → 获取图表操作
└── 渲染图表内容
```

#### 3. 数据加载流程
```
用户操作/页面初始化
    ↓
筛选器更新 → QueryParamsContext
    ↓
组件调用 chartActions.refreshChart() 或 statisticsActions.refreshStatistics()
    ↓
上下文系统调用 DataSourceManager
    ↓
DataSourceManager 选择合适的策略 (ChartDataLoadingStrategy/StatisticsDataLoadingStrategy)
    ↓
策略通过 API Decorator 自动注入筛选参数
    ↓
执行 API 调用 → 数据转换 → 存储到 DataSourceManager
    ↓
上下文系统更新 → 组件自动响应数据变化
```

### 关键数据流特点

#### 🎯 零透传设计
- **无Props传递**：所有数据通过上下文系统传递，组件间无直接数据传递
- **无Emit事件**：所有操作通过上下文方法调用，无需事件冒泡
- **无组件暴露**：组件无需暴露方法，完全通过上下文通信

#### 🔄 自动化流程
- **自动参数注入**：API Decorator 自动获取当前筛选参数
- **自动策略选择**：根据配置自动选择合适的数据加载策略
- **自动状态管理**：加载状态、错误状态自动管理和更新

#### 📊 统一数据管理
- **统一缓存**：所有数据通过 DataSourceManager 统一缓存
- **统一配置**：所有配置通过 TabConfigManager 统一管理
- **统一类型**：完整的 TypeScript 类型支持

### 组件交互时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 筛选器组件
    participant C as ChartGroup组件
    participant CA as ChartActions上下文
    participant DM as DataSourceManager
    participant S as 数据加载策略
    participant API as API调用

    Note over U,API: 页面初始化流程
    U->>F: 页面加载
    F->>CA: 触发初始数据加载
    CA->>DM: 请求图表数据
    DM->>S: 选择加载策略
    S->>API: 调用API (自动注入参数)
    API-->>S: 返回数据
    S->>DM: 存储转换后的数据
    DM-->>CA: 返回数据
    CA-->>C: 上下文数据更新
    C-->>U: 渲染图表

    Note over U,API: 筛选器交互流程
    U->>F: 修改筛选条件
    F->>CA: 调用 refreshChart()
    CA->>DM: 请求刷新数据
    Note over DM: 自动获取最新筛选参数
    DM->>S: 使用新参数重新加载
    S->>API: 调用API (新参数)
    API-->>S: 返回新数据
    S->>DM: 更新缓存数据
    DM-->>CA: 返回新数据
    CA-->>C: 上下文数据更新
    C-->>U: 重新渲染图表

    Note over U,API: 图表操作流程
    U->>C: 点击图表 (下探)
    C->>CA: 调用 handleDrillDown()
    CA->>DM: 请求下探数据
    DM->>S: 选择下探策略
    S->>API: 调用下探API
    API-->>S: 返回下探数据
    S->>DM: 存储下探数据
    DM-->>CA: 返回下探数据
    CA-->>C: 更新图表配置和数据
    C-->>U: 渲染下探图表
```

### 上下文系统优势对比

#### 传统Props传递 vs 上下文系统

```mermaid
graph LR
    subgraph "传统Props传递"
        A1[父组件] --> B1[子组件1]
        A1 --> C1[子组件2]
        B1 --> D1[孙组件1]
        B1 --> E1[孙组件2]
        C1 --> F1[孙组件3]

        style A1 fill:#ffcdd2
        style B1 fill:#ffcdd2
        style C1 fill:#ffcdd2
        style D1 fill:#ffcdd2
        style E1 fill:#ffcdd2
        style F1 fill:#ffcdd2
    end

    subgraph "上下文系统"
        A2[Context Provider]
        B2[组件1] -.-> A2
        C2[组件2] -.-> A2
        D2[组件3] -.-> A2
        E2[组件4] -.-> A2
        F2[组件5] -.-> A2

        style A2 fill:#c8e6c9
        style B2 fill:#c8e6c9
        style C2 fill:#c8e6c9
        style D2 fill:#c8e6c9
        style E2 fill:#c8e6c9
        style F2 fill:#c8e6c9
    end
```

| 特性 | 传统Props传递 | 上下文系统 |
|------|---------------|------------|
| **数据传递层数** | 需要逐层传递 | 直接注入任意层级 |
| **组件耦合度** | 高度耦合 | 完全解耦 |
| **维护成本** | 修改需要改多个组件 | 只需修改上下文 |
| **类型安全** | 容易出错 | 完整类型推断 |
| **代码复用** | 难以复用 | 高度可复用 |
| **测试难度** | 需要模拟整个组件树 | 可单独测试 |

### 实际组件层级数据流示例

```mermaid
graph TD
    subgraph "实际组件结构"
        A[UserOperationStatisticsDashboard.vue<br/>🔥 Provide所有上下文]

        subgraph "Tab容器层"
            B[TabContainer<br/>📋 Tab切换逻辑]
        end

        subgraph "内容渲染层"
            C[ChartGroupContainer.vue<br/>📊 统计数据 + 图表组]
            D[ChartGroup.vue<br/>📈 图表组渲染]
        end

        subgraph "具体图表层"
            E[BarChart.vue<br/>📊 柱状图]
            F[LineChart.vue<br/>📈 折线图]
            G[PieChart.vue<br/>🥧 饼图]
        end

        subgraph "上下文数据流"
            H[ChartActions<br/>🎯 图表操作]
            I[StatisticsActions<br/>📊 统计操作]
            J[DataSourceManager<br/>🗄️ 数据管理]
        end
    end

    %% 组件层级关系
    A --> B
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G

    %% 上下文注入关系 (虚线表示inject)
    C -.-> I
    C -.-> H
    D -.-> H
    E -.-> H
    F -.-> H
    G -.-> H

    %% 上下文内部依赖
    H --> J
    I --> J

    style A fill:#e3f2fd
    style H fill:#fff3e0
    style I fill:#fff3e0
    style J fill:#f3e5f5
```

### 策略模式统一优化成果

#### 🔧 下探功能统一改进
**问题**：之前存在重复的数据转换方法，代码冗余
**解决**：
- 删除重复的 `convertApiResponseToDrillDownData` 方法
- 统一使用现有的 `convertMonthlyDataToDrillDownData` 方法
- 通过 `transformSecondApiDataToChartData` 工具函数处理API响应
- 保持数据流一致性，遵循DRY原则

#### 📊 数据流优化
```typescript
// 优化后的下探数据流
fetchRawDrillDownData() {
  // 1. API调用 → 获取二级来源数据
  const apiResponse = await this.apiDecorator.decorateApiCall(queryAPI, params);
  
  // 2. 数据转换 → 使用现有工具函数
  const transformedData = transformSecondApiDataToChartData(apiResponse);
  
  // 3. 下探转换 → 复用现有方法
  return this.convertMonthlyDataToDrillDownData(transformedData, parentChannelKey);
}
```

#### 🎨 图表配置更新策略集成

**设计目标**：将图表配置更新逻辑集成到现有数据加载策略中，避免过度设计和代码冗余。

**核心思路**：
- **避免独立策略体系**：不创建单独的配置更新策略，而是扩展现有数据加载策略
- **统一策略选择**：数据加载和配置更新使用同一套策略选择逻辑
- **职责统一**：每个策略类统一处理数据加载、转换和配置更新

**实现方案**：
```typescript
// 在 useUnifiedDataLoader.ts 中统一处理
export const updateChartConfigWithData = async (
  chartId: string,
  data: ChartDataItem[],
  newDataSource?: string
): Promise<void> => {
  const strategy = chartDataLoadingStrategyFactory.getStrategy(chartId);
  
  if (strategy) {
    // 使用同一个策略处理配置更新
    const updateConfig = await strategy.updateChartConfig(
      currentChartConfig,
      data,
      newDataSource
    );
    
    // 更新图表配置
    updateChartConfig(chartId, updateConfig);
  } else {
    console.warn(`未找到图表 ${chartId} 对应的策略`);
  }
};
```

**架构优势**：
- **消除冗余代码**：不再需要维护大量的 if-else 分支
- **统一策略选择**：数据加载和配置更新使用同一套逻辑
- **减少重复实现**：避免在不同地方重复实现策略选择
- **保证逻辑一致性**：确保数据转换和配置更新的逻辑保持一致
- **简化架构复杂度**：无需维护额外的策略体系

#### ✨ 核心优势
- **代码复用**：避免重复实现相同功能
- **维护性**：单一数据转换逻辑，易于维护
- **一致性**：所有下探功能使用统一的数据处理流程
- **扩展性**：新增下探功能只需实现具体的API调用逻辑
- **职责统一**：每个策略类统一处理数据加载、转换和配置更新
- **减少维护成本**：只需维护一套策略体系

### 关键优势
1. **零手动传参**：筛选参数自动注入，无需手动获取和传递
2. **统一架构**：图表和统计数据使用相同的模式
3. **自动缓存**：数据源管理器自动处理缓存
4. **类型安全**：完整的TypeScript支持
5. **易扩展**：新增功能只需添加策略
6. **零层级限制**：任意深度的组件都可以直接注入上下文
7. **完全解耦**：组件间无直接依赖，易于测试和维护

### 内置状态管理优势

| 特性 | 外部状态管理 | 内置状态管理 | 优势 |
|------|--------------|--------------|------|
| **注入顺序** | 需要先provide状态 | 无顺序要求 | 解决注入问题 |
| **状态访问** | 需要单独inject | 直接通过操作上下文 | 简化使用 |
| **类型安全** | 可能返回null | 完整类型推断 | 类型安全 |
| **维护成本** | 多个上下文管理 | 单一上下文 | 降低复杂度 |
| **错误处理** | 需要容错处理 | 内置默认值 | 更加稳定 |

## 📊 架构优势

| 特性 | 传统方式 | 统一架构 | 改进 |
|------|----------|----------|------|
| 数据传递 | Props层层传递 | 上下文直接注入 | 0层传递 |
| 状态管理 | 分散在各组件 | 内置状态管理 | 统一管理 |
| 参数传递 | 手动获取传递 | 装饰器自动注入 | 零手动传参 |
| 数据缓存 | 各自管理 | 统一数据源管理器 | 统一缓存 |
| 代码复用 | 重复实现 | 策略模式 | 高度复用 |
| 类型安全 | 部分支持 | 完整TypeScript | 完全类型安全 |
| 上下文注入 | 顺序敏感 | 内置状态管理 | 无顺序问题 |
| 组件耦合 | 高度耦合 | 完全解耦 | 易于测试 |

这个统一架构实现了真正的**零透传、零手动传参、统一管理**，为统计看板提供了强大、灵活、易维护的技术基础。

## 🛠️ 实践指南

### 新增策略的标准流程

1. **创建策略类**：继承对应的基类，实现必要的抽象方法
2. **注册策略**：通过工厂类注册新策略
3. **配置使用**：在配置中指定策略类型

#### 新增图表策略示例

```typescript
// 1. 创建新的图表策略
export class NewChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'new-chart';
  readonly supportedChartTypes = ['newChartType'];
  
  // 实现数据加载
  protected async fetchApiData(params: commonQueryParams) {
    const { queryNewChartData } = await import('../api');
    return await queryNewChartData(params);
  }
  
  // 实现配置更新
  async updateChartConfig(
    chartConfig: ChartConfig, 
    data: ChartDataItem[], 
    newDataSource?: string
  ): Promise<Partial<ChartConfig>> {
    // 动态导入配置管理器
    const { updateNewChartData } = await import('../config/newChartConfig');
    
    // 调用专门的配置更新方法
    const updateConfig = updateNewChartData(chartConfig, data);
    
    // 使用基类的通用增强方法
    return this.enhanceUpdateConfig(updateConfig, data, newDataSource);
  }
}

// 2. 在工厂类中注册策略
private initializeStrategies(): void {
  this.strategies = [
    new ClueSourceChartStrategy(),
    new UtmChartStrategy(),
    new ClueEffectivenessChartStrategy(),
    new NewChartStrategy(), // 添加新策略
  ];
}
```

#### 配置更新策略最佳实践

1. **复用现有配置管理器**：
   - 优先使用现有的图表配置管理器（如 `clueSourceChartConfig.ts`）
   - 避免重复实现相同的配置逻辑

2. **使用通用增强方法**：
   - 调用 `this.enhanceUpdateConfig()` 添加通用状态更新
   - 自动处理 loading、isEmpty、currentDataSource 等状态

3. **动态导入配置管理器**：
   - 使用 `await import()` 动态导入，提高性能
   - 避免不必要的模块加载

4. **数据源切换支持**：
   - 如果图表支持数据源切换，实现 `getDataSourceSwitchInfo()` 方法
   - 如果不支持，使用基类的默认实现即可

### 组件使用上下文

```typescript
// 标准使用模式
const chartActions = useChartActionsOptional();
const chartData = useChartDataOptional();

// 操作调用
chartActions.refreshChart('chartId');

// 状态获取
const isLoading = chartActions.chartLoadingStates['chartId'];
```

## 🔍 故障排查

### 常见问题

1. **策略选择失败**：检查图表配置是否匹配策略的 `supportedChartTypes`
2. **数据加载失败**：验证筛选参数和API接口状态
3. **上下文注入问题**：现已通过内置状态管理解决

### 调试技巧

- **策略日志**：在策略中添加关键步骤日志
- **数据源检查**：验证 `dataSourceManager` 中的数据状态
- **上下文状态**：检查 `chartLoadingStates` 和 `chartErrorStates`

## 📈 性能优化

- **缓存策略**：数据源管理器自动缓存，策略实例复用
- **按需加载**：API方法和组件支持动态导入
- **内存管理**：提供数据清理和状态重置机制

---

**总结**：统计看板统一架构通过上下文系统、策略模式、数据源管理器和API装饰器的完美结合，实现了高度解耦、易扩展、高性能的现代化前端架构。这套架构不仅解决了当前的技术问题，也为未来的功能扩展奠定了坚实的基础。
