# 统计看板核心架构指南

## 📋 架构概述

统计看板采用现代化的Vue 3架构设计，通过**统一查询参数管理器 + 依赖注入容器 + 策略模式 + 上下文系统**实现了高度解耦的组件化架构。

### 🎯 核心设计理念

- **零透传架构**：通过上下文系统消除组件间的数据传递和事件冒泡
- **统一参数管理**：全局和局部参数的统一管理，支持参数作用域隔离
- **依赖注入模式**：策略类通过依赖容器获取所需依赖，确保组件可用性
- **策略化处理**：数据加载和处理逻辑策略化，支持灵活扩展
- **内置状态管理**：操作上下文内置状态管理，避免注入顺序问题

### ✨ 关键架构特点

| 特性 | 传统方式 | 新架构 | 优势 |
|------|----------|--------|------|
| 数据传递 | Props层层传递 | 上下文直接注入 | 零层级传递 |
| 参数管理 | 手动获取传递 | 自动注入管理 | 零手动传参 |
| 状态管理 | 分散在各组件 | 内置统一管理 | 统一状态控制 |
| 组件耦合 | 高度耦合 | 完全解耦 | 易于测试维护 |
| 扩展性 | 重复实现 | 策略模式 | 高度可复用 |

## 🏗️ 架构层次图

### 系统整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    统计看板主组件                              │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │ Provide 上下文
┌─────────────────────▼───────────────────────────────────────┐
│                   上下文系统层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ChartActions    │  │StatisticsActions│  │UnifiedQuery  │ │
│  │ (内置状态管理)   │  │ (内置状态管理)   │  │ParamsManager │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 依赖注入
┌─────────────────────▼───────────────────────────────────────┐
│                 策略依赖注入容器                              │
│              StrategyDependencyContainer                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  查询参数管理器注入 + 数据源管理器注入                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 策略选择
┌─────────────────────▼───────────────────────────────────────┐
│                   策略工厂层                                  │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartDataLoading │              │StatisticsDataLoading    │ │
│  │StrategyFactory  │              │StrategyFactory          │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 具体策略实现
┌─────────────────────▼───────────────────────────────────────┐
│                   策略实现层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ClueSourceChart  │  │ClueOverview     │  │UTMChart      │ │
│  │Strategy         │  │Statistics       │  │Strategy      │ │
│  │                 │  │Strategy         │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ API调用
┌─────────────────────▼───────────────────────────────────────┐
│                    API调用层                                 │
│              ApiCallDecorator                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  自动注入筛选参数 → API调用 → 数据转换 → 缓存存储          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 组件关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    主组件层                                  │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   容器组件层                                 │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartGroup       │              │StatisticsContainer      │ │
│  │Container        │              │                         │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   展示组件层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │EChartsComponent │  │StatisticsCard   │  │FilterPanel   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心系统详解

### 3.1 统一查询参数管理器

#### 设计原理
统一查询参数管理器是整个架构的参数中枢，负责管理全局和局部查询参数，支持参数作用域隔离和自动合并。

#### 核心功能
- **参数作用域管理**：支持全局参数和组级别参数的独立管理
- **自动参数合并**：根据作用域优先级自动合并参数
- **参数变化监听**：提供参数变化的响应式监听机制
- **防抖处理**：避免参数频繁变化导致的重复API调用

#### 关键接口
```typescript
interface UnifiedQueryParamsManager {
  // 参数管理
  setGlobalParams(params: Partial<commonQueryParams>): void;
  setGroupParams(groupId: string, params: Partial<commonQueryParams>): void;
  getFinalParams(groupId?: string): commonQueryParams;

  // 监听机制
  onParamsChange(callback: (params: commonQueryParams) => void): void;

  // 作用域管理
  getParamScope(groupId?: string): 'global' | 'group';
  clearGroupParams(groupId: string): void;
}
```

### 3.2 依赖注入容器

#### 设计思路
策略依赖注入容器采用单例模式，统一管理策略实例的依赖注入，确保所有策略共享同一个数据源管理器和参数管理器实例。

#### 核心职责
- **依赖管理**：统一管理查询参数管理器和数据源管理器的注入
- **策略创建**：提供策略实例的统一创建机制
- **单例保证**：确保依赖的一致性和数据同步

#### 容器架构
```typescript
class StrategyDependencyContainer {
  private dataSourceManager: IDataSourceManager;
  private queryParamsManager?: UnifiedQueryParamsManager;

  // 策略创建方法
  createChartDataLoadingStrategy<T>(StrategyClass: new () => T): T;
  createStatisticsDataLoadingStrategy<T>(StrategyClass: new () => T): T;

  // 依赖获取方法
  getDataSourceManager(): IDataSourceManager;
  getQueryParamsManager(): UnifiedQueryParamsManager;
}
```

### 3.3 策略模式实现

#### 策略体系架构
策略模式在统计看板中分为三个主要类别：
- **图表数据加载策略**：处理各类图表的数据获取和转换
- **统计数据加载策略**：处理统计卡片的数据获取和计算
- **图表下探策略**：处理图表的交互式下探功能

#### 策略基类设计
```typescript
abstract class BaseStrategy<TConfig, TData> {
  protected dataSourceManager: IDataSourceManager;

  // 模板方法：标准数据加载流程
  async loadData(config: TConfig, customParams?: any): Promise<TData[]>;

  // 抽象方法：具体实现由子类定义
  protected abstract fetchApiData(params: commonQueryParams): Promise<any>;
  protected abstract transformData(apiData: any): Promise<TData[]>;
}
```

#### 策略工厂模式
```typescript
class ChartDataLoadingStrategyFactory {
  private strategies: IChartDataLoadingStrategy[] = [];

  getStrategy(chartId: string): IChartDataLoadingStrategy | null;
  registerStrategy(strategy: IChartDataLoadingStrategy): void;
}
```

### 3.4 上下文系统

#### 设计模式
上下文系统基于Vue 3的Provide/Inject模式，实现了完全的组件解耦和零透传架构。

#### 上下文类型
- **ChartActionContext**：图表操作上下文，提供图表刷新、切换、下探等操作
- **StatisticsActionContext**：统计数据操作上下文，提供统计数据的刷新和管理
- **ChartDataContext**：图表数据上下文，提供图表数据的访问接口
- **StatisticsDataContext**：统计数据上下文，提供统计数据的访问接口

#### 内置状态管理
每个操作上下文都内置了状态管理，避免了外部状态上下文的依赖：
```typescript
interface ChartActionContext {
  // 操作方法
  refreshChart(chartId: string): void;
  switchChartDataSource(chartId: string, newDataSource: string): void;

  // 内置状态
  chartLoadingStates: Record<string, boolean>;
  chartErrorStates: Record<string, string | null>;
}
```

## 🔄 数据流程机制

### 参数管理流程
```
筛选器组件 → 统一查询参数管理器 → 参数变化监听 → 自动数据刷新
```

### 数据加载流程
```
组件操作 → 上下文系统 → 策略工厂 → 具体策略 → API调用 → 数据转换 → 缓存存储 → 组件更新
```

### 组件通信流程
```
子组件 → inject上下文 → 调用上下文方法 → 触发数据更新 → 响应式更新所有相关组件
```

## 🛠️ 实践指南

### 开发规范
1. **新增图表**：继承BaseChartDataLoadingStrategy，实现具体的数据获取和转换逻辑
2. **新增统计**：继承BaseStatisticsDataLoadingStrategy，实现统计数据的计算逻辑
3. **组件开发**：使用useXXXOptional hooks获取上下文，避免直接的组件通信

### 扩展方法
1. **策略注册**：通过策略工厂注册新的数据处理策略
2. **上下文扩展**：在现有上下文中添加新的操作方法
3. **参数扩展**：在统一参数管理器中添加新的参数类型

### 最佳实践
- 保持策略类的单一职责，专注于特定的数据处理逻辑
- 使用依赖注入容器创建策略实例，确保依赖的一致性
- 通过上下文系统进行组件通信，避免直接的组件依赖
- 利用统一参数管理器的防抖机制，优化API调用频率

---

**总结**：统计看板核心架构通过统一查询参数管理器、依赖注入容器、策略模式和上下文系统的有机结合，实现了高度解耦、易扩展、高性能的现代化前端架构。这套架构不仅解决了组件间的紧耦合问题，也为未来的功能扩展提供了坚实的技术基础。


