# 统计看板核心架构指南

## 📋 架构概述

统计看板采用现代化的Vue 3架构设计，通过**统一查询参数管理器 + 依赖注入容器 + 策略模式 + 上下文系统**实现了高度解耦的组件化架构。

### 🎯 核心设计理念

- **零透传架构**：通过上下文系统消除组件间的数据传递和事件冒泡
- **统一参数管理**：全局和局部参数的统一管理，支持参数作用域隔离
- **依赖注入模式**：策略类通过依赖容器获取所需依赖，确保组件可用性
- **策略化处理**：数据加载和处理逻辑策略化，支持灵活扩展
- **内置状态管理**：操作上下文内置状态管理，避免注入顺序问题

### ✨ 关键架构特点

| 特性 | 传统方式 | 新架构 | 优势 |
|------|----------|--------|------|
| 数据传递 | Props层层传递 | 上下文直接注入 | 零层级传递 |
| 参数管理 | 手动获取传递 | 自动注入管理 | 零手动传参 |
| 状态管理 | 分散在各组件 | 内置统一管理 | 统一状态控制 |
| 组件耦合 | 高度耦合 | 完全解耦 | 易于测试维护 |
| 扩展性 | 重复实现 | 策略模式 | 高度可复用 |

## 🏗️ 架构层次图

### 系统整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    统计看板主组件                              │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │ Provide 上下文
┌─────────────────────▼───────────────────────────────────────┐
│                   上下文系统层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ ChartActions    │  │StatisticsActions│  │UnifiedQuery  │ │
│  │ (内置状态管理)   │  │ (内置状态管理)   │  │ParamsManager │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 依赖注入
┌─────────────────────▼───────────────────────────────────────┐
│                 策略依赖注入容器                              │
│              StrategyDependencyContainer                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  查询参数管理器注入 + 数据源管理器注入                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 策略选择
┌─────────────────────▼───────────────────────────────────────┐
│                   策略工厂层                                  │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartDataLoading │              │StatisticsDataLoading    │ │
│  │StrategyFactory  │              │StrategyFactory          │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 具体策略实现
┌─────────────────────▼───────────────────────────────────────┐
│                   策略实现层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ClueSourceChart  │  │ClueOverview     │  │UTMChart      │ │
│  │Strategy         │  │Statistics       │  │Strategy      │ │
│  │                 │  │Strategy         │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │ 直接API调用
┌─────────────────────▼───────────────────────────────────────┐
│                    API调用层                                 │
│              策略内置API调用机制                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │  查询参数管理器获取参数 → API调用 → 数据转换              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 组件关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    主组件层                                  │
│              UserOperationStatisticsDashboard               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   容器组件层                                 │
│  ┌─────────────────┐              ┌─────────────────────────┐ │
│  │ChartGroup       │              │StatisticsContainer      │ │
│  │Container        │              │                         │ │
│  └─────────────────┘              └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   展示组件层                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │EChartsComponent │  │StatisticsCard   │  │FilterPanel   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 核心系统详解

### 3.1 统一查询参数管理器

#### 设计原理
统一查询参数管理器是整个架构的参数中枢，负责管理全局和局部查询参数，支持参数作用域隔离和自动合并。它通过依赖注入的方式为策略类提供统一的参数获取接口。

#### 核心功能
- **参数作用域管理**：支持全局参数和组级别参数的独立管理
- **自动参数合并**：根据作用域优先级自动合并参数
- **参数变化监听**：提供参数变化的响应式监听机制
- **防抖处理**：避免参数频繁变化导致的重复API调用
- **依赖注入支持**：通过策略依赖注入容器为策略类提供参数管理器实例

#### 关键组件协作

**1. 主组件 (UserOperationStatisticsDashboard.vue)**
- 提供统一查询参数管理器上下文
- 将参数管理器注入到策略依赖容器
- 设置全局参数变化监听器

**2. 全局筛选器 (FilterPanel.vue)**
- 管理全局筛选参数
- 通过 `setGlobalParams()` 更新全局参数
- 支持用户权限数据的自动集成

**3. 局部筛选器 (LocalFilterPanel.vue)**
- 管理组级别的局部参数
- 通过 `setGroupParams(groupId, params)` 更新组参数
- 支持参数作用域隔离

**4. 初始化上下文 (useInitializationContext.ts)**
- 管理组件初始化状态
- 确保参数管理器在所有组件就绪后开始工作
- 协调组件间的初始化顺序

#### 关键接口
```typescript
interface UnifiedQueryParamsManager {
  // 参数管理
  setGlobalParams(params: Partial<commonQueryParams>): void;
  setGroupParams(groupId: string, params: Partial<commonQueryParams>): void;
  getFinalParams(groupId?: string): commonQueryParams;

  // 监听机制
  onParamsChange(callback: (params: commonQueryParams, scope: ParamScope, groupId?: string) => void): void;

  // 作用域管理
  getParamScope(groupId?: string): 'global' | 'group';
  clearGroupParams(groupId: string): void;

  // 初始化管理
  finishInitialization(): void;
}
```

#### 参数流转机制
```
FilterPanel → setGlobalParams() → 全局参数更新 → 触发全局数据刷新
LocalFilterPanel → setGroupParams(groupId) → 组参数更新 → 触发组数据刷新
策略类 → getFinalParams(groupId) → 获取合并后的最终参数 → API调用
```

### 3.2 依赖注入容器

#### 设计思路
策略依赖注入容器采用单例模式，统一管理策略实例的依赖注入，确保所有策略共享同一个数据源管理器和参数管理器实例。它是连接上下文系统和策略层的关键桥梁。

#### 核心职责
- **依赖管理**：统一管理查询参数管理器和数据源管理器的注入
- **策略创建**：提供策略实例的统一创建机制
- **单例保证**：确保依赖的一致性和数据同步
- **参数管理器注入**：接收主组件提供的查询参数管理器实例

#### 注入流程
```
UserOperationStatisticsDashboard.vue
    ↓ 创建并注入
StrategyDependencyContainer
    ↓ 提供依赖
BaseStrategy.getQueryParamsManager()
    ↓ 获取参数
策略类执行数据加载
```

#### 容器架构
```typescript
class StrategyDependencyContainer {
  private dataSourceManager: IDataSourceManager;
  private queryParamsManager?: UnifiedQueryParamsManager;

  // 依赖注入方法
  setQueryParamsManager(manager: UnifiedQueryParamsManager): void;

  // 策略创建方法
  createChartDataLoadingStrategy<T>(StrategyClass: new () => T): T;
  createStatisticsDataLoadingStrategy<T>(StrategyClass: new () => T): T;

  // 依赖获取方法
  getDataSourceManager(): IDataSourceManager;
  getQueryParamsManager(): UnifiedQueryParamsManager;
}
```

### 3.3 策略模式实现

#### 策略体系架构
策略模式在统计看板中分为三个主要类别：
- **图表数据加载策略**：处理各类图表的数据获取和转换
- **统计数据加载策略**：处理统计卡片的数据获取和计算
- **图表下探策略**：处理图表的交互式下探功能

#### 策略基类设计与参数获取机制
```typescript
abstract class BaseStrategy<TConfig, TData> {
  protected dataSourceManager: IDataSourceManager;

  // 从依赖容器获取查询参数管理器
  protected getQueryParamsManager(): UnifiedQueryParamsManager | undefined {
    const container = getStrategyDependencyContainer();
    return container.getQueryParamsManager();
  }

  // 执行数据加载的核心流程
  protected async performDataLoading(config: TConfig, context?: any): Promise<TData> {
    // 🔥 使用查询参数管理器获取参数
    const queryParamsManager = this.getQueryParamsManager();
    const finalParams = queryParamsManager.getFinalParams(context?.groupId);

    // 直接调用API
    const apiData = await this.fetchApiData(finalParams, config);

    // 数据转换
    const transformedData = await this.transformData(apiData, config);

    return transformedData;
  }

  // 抽象方法：具体实现由子类定义
  protected abstract fetchApiData(params: commonQueryParams, config: TConfig): Promise<any>;
  protected abstract transformData(apiData: any, config: TConfig): Promise<TData>;
}
```

#### 策略工厂模式
```typescript
class ChartDataLoadingStrategyFactory {
  private strategies: IChartDataLoadingStrategy[] = [];

  getStrategy(chartId: string): IChartDataLoadingStrategy | null;
  registerStrategy(strategy: IChartDataLoadingStrategy): void;
}
```

#### 关键变更：移除API装饰器
- **原有方式**：策略类通过API装饰器获取参数并调用API
- **新实现**：策略类直接通过查询参数管理器获取参数，然后直接调用API
- **优势**：简化了调用链路，减少了中间层，提高了性能和可维护性

### 3.4 上下文系统

#### 设计模式
上下文系统基于Vue 3的Provide/Inject模式，实现了完全的组件解耦和零透传架构。通过多个专门的hooks协同工作，形成完整的上下文生态。

#### 核心Hooks架构

**1. useChartActions - 图表操作上下文**
- **职责**：提供图表操作的统一接口和内置状态管理
- **特点**：
  - 内置loading和error状态管理，避免外部状态依赖
  - 集成配置管理器和数据源管理器
  - 支持图表刷新、切换、下探等操作
  - 零透传设计，组件可直接inject使用

**2. useTabConfigManager - 配置管理中心**
- **职责**：统一管理Tab配置和图表配置
- **特点**：
  - 全局单例模式，确保配置状态在所有组件间共享
  - 支持权限控制和响应式配置管理
  - 消除重复存储，成为唯一的配置数据源
  - Tab级别的loading状态管理

**3. useDataSourceManager - 数据源管理器**
- **职责**：统一管理数据源映射和数据获取逻辑
- **特点**：
  - 全局单例模式，解决多实例化和数据不同步问题
  - 支持泛型的类型安全数据访问
  - 元数据管理和统计信息
  - 动态数据源注册和分类管理

**4. useUnifiedDataLoader - 统一数据加载器**
- **职责**：整合策略系统，提供统一的数据加载接口
- **特点**：
  - 策略缓存机制，提高性能
  - 加载状态管理和错误处理
  - 支持图表和统计数据的统一加载
  - 与上下文系统无缝集成

#### 架构优势

**1. 消除重复存储**
```typescript
// ❌ 原有问题：多处重复存储配置
// useChartActions.ts: chartConfigs
// useTabConfigManager.ts: chartConfigs
// useStatisticDashboard.ts: tabs

// ✅ 优化后：单一存储源
// useTabConfigManager.ts (唯一的配置存储)
const chartConfigs = reactive<Record<string, ChartConfig>>({});

// useChartActions.ts (只提供访问接口)
const getChartConfig = (chartId: string) => {
  return tabConfigManager.getChartConfig(chartId);
};
```

**2. 内置状态管理**
```typescript
// useChartActions内置状态，避免注入顺序问题
const chartLoadingStates = reactive<Record<string, boolean>>({});
const chartErrorStates = reactive<Record<string, string | null>>({});
```

**3. 全局单例保证**
```typescript
// 确保数据源管理器全局唯一
let globalDataSourceManager: IDataSourceManager | null = null;
export function useGlobalDataSourceManager(): IDataSourceManager {
  if (!globalDataSourceManager) {
    globalDataSourceManager = createDataSourceManager();
  }
  return globalDataSourceManager;
}
```

## 🔄 数据流程机制

### 参数管理流程
```
全局筛选：FilterPanel → setGlobalParams() → 统一查询参数管理器 → 全局参数变化监听 → 自动数据刷新
局部筛选：LocalFilterPanel → setGroupParams(groupId) → 统一查询参数管理器 → 组参数变化监听 → 组数据刷新
```

### 数据加载流程
```
组件操作 → 上下文系统 → useUnifiedDataLoader → 策略工厂 → 具体策略 → 查询参数管理器获取参数 → 直接API调用 → 数据转换 → 组件更新
```

### 配置管理流程
```
useTabConfigManager (唯一配置源) → useChartActions (访问接口) → 组件 (配置消费)
```

### 组件通信流程
```
子组件 → inject上下文 → 调用上下文方法 → 触发数据更新 → 响应式更新所有相关组件
```

### 详细数据加载时序
```
1. 用户操作筛选器
2. FilterPanel/LocalFilterPanel 更新参数管理器
3. 参数管理器触发变化监听
4. 上下文系统调用相应的刷新方法
5. useUnifiedDataLoader 选择合适的策略
6. 策略类通过 getQueryParamsManager() 获取参数管理器
7. 调用 getFinalParams(groupId) 获取最终参数
8. 直接调用API获取数据
9. 数据转换和验证
10. 存储到全局数据源管理器
11. 组件响应式更新
```

### Hooks协作关系
```
useTabConfigManager (配置中心)
    ↓ 提供配置
useChartActions (操作上下文)
    ↓ 调用加载器
useUnifiedDataLoader (数据加载)
    ↓ 使用策略
策略类 (具体实现)
    ↓ 存储数据
useDataSourceManager (数据存储)
```

## 🛠️ 实践指南

### 开发规范

#### 1. 新增图表策略
```typescript
export class NewChartStrategy extends BaseChartDataLoadingStrategy {
  readonly strategyType = 'new-chart';
  readonly supportedChartTypes = ['newType'];

  protected async fetchApiData(params: commonQueryParams, config: ChartConfig) {
    // 直接调用API，参数已通过查询参数管理器获取
    const { queryNewChartData } = await import('../api');
    return await queryNewChartData(params);
  }

  protected async transformData(apiData: any, config: ChartConfig) {
    // 实现数据转换逻辑
    return transformedData;
  }
}
```

#### 2. 配置管理最佳实践
- **统一配置源**：使用useTabConfigManager作为唯一的配置存储
- **避免重复存储**：其他hooks只提供访问接口，不重复存储配置
- **全局单例**：确保配置管理器在所有组件间共享相同状态

#### 3. 参数管理最佳实践
- **全局参数**：通过FilterPanel的`setGlobalParams()`设置
- **局部参数**：通过LocalFilterPanel的`setGroupParams(groupId, params)`设置
- **参数获取**：策略类通过`getFinalParams(groupId)`获取合并后的最终参数

#### 4. 数据源管理最佳实践
- **全局单例**：使用`useGlobalDataSourceManager()`确保数据一致性
- **类型安全**：利用泛型支持不同数据类型的安全访问
- **元数据管理**：为数据源提供完整的元信息和分类

#### 5. 组件开发规范
- 使用`useXXXOptional` hooks获取上下文，避免直接的组件通信
- 通过统一查询参数管理器进行参数传递，而非props或emit
- 利用初始化上下文管理组件的初始化顺序
- 遵循单一职责原则，每个hook专注特定功能

### 扩展方法

#### 1. 策略注册
```typescript
// 在策略工厂中注册新策略
private initializeStrategies(): void {
  this.strategies = [
    new ClueSourceChartStrategy(),
    new NewChartStrategy(), // 添加新策略
  ];
}
```

#### 2. 配置扩展
```typescript
// 在useTabConfigManager中添加新的Tab配置
const newTabConfig: TabConfig = {
  id: 'newTab',
  name: '新功能Tab',
  layout: 'grid',
  groups: [/* 配置组 */]
};
tabConfigManager.addTabConfig(newTabConfig);
```

#### 3. 数据源扩展
```typescript
// 注册新的数据源类型，在策略类中使用
dataSourceManager.setDataSource('newDataSource', data, {
  name: '新数据源',
  category: 'custom',
  type: 'chart'
});
```

#### 4. 参数扩展
在统一参数管理器中添加新的参数类型，确保类型安全：
```typescript
interface ExtendedQueryParams extends commonQueryParams {
  newParam: string;
}
```

### 最佳实践

#### 1. 架构设计原则
- **单一职责**：每个hook专注特定功能，避免职责混乱
- **避免重复存储**：使用统一的配置和数据源管理，消除重复
- **全局单例**：关键管理器使用单例模式，确保状态一致性

#### 2. 参数管理
- 利用参数作用域隔离，避免全局参数和局部参数冲突
- 使用防抖机制，避免参数频繁变化导致的重复API调用
- 通过初始化上下文确保参数管理器在组件就绪后开始工作

#### 3. 策略设计
- 保持策略类的单一职责，专注于特定的数据处理逻辑
- 通过依赖注入容器获取查询参数管理器，确保参数可用性
- 实现完整的错误处理和数据验证机制

#### 4. 组件通信
- 通过上下文系统进行组件通信，避免直接的组件依赖
- 使用响应式数据更新机制，确保组件状态同步
- 利用内置状态管理，避免外部状态上下文的复杂性

#### 5. 性能优化
- 利用策略缓存机制，避免重复创建策略实例
- 使用全局数据源管理器，减少数据重复存储
- 通过统一数据加载器，优化加载流程和错误处理

### 关键架构优势

#### 1. 简化的调用链路
- **移除API装饰器**：策略类直接通过查询参数管理器获取参数
- **减少中间层**：从"策略→装饰器→API"简化为"策略→参数管理器→API"
- **提高性能**：减少了不必要的抽象层，提升了执行效率

#### 2. 统一的参数管理
- **全局和局部参数统一管理**：通过同一个参数管理器处理不同作用域的参数
- **自动参数合并**：根据优先级自动合并全局和局部参数
- **响应式参数变化**：参数变化自动触发相关组件的数据刷新

#### 3. 完善的依赖注入
- **单例模式**：确保所有策略共享同一个参数管理器实例
- **依赖隔离**：通过容器管理依赖，避免直接耦合
- **类型安全**：完整的TypeScript类型支持

## 📋 关键组件总览

### 核心Hooks清单

| Hook名称 | 文件路径 | 主要职责 | 设计模式 |
|----------|----------|----------|----------|
| **useUnifiedQueryParams** | hooks/useUnifiedQueryParams.ts | 统一查询参数管理 | 单例模式 |
| **useTabConfigManager** | hooks/useTabConfigManager.ts | Tab和图表配置管理 | 全局单例 |
| **useDataSourceManager** | hooks/useDataSourceManager.ts | 数据源统一管理 | 全局单例 |
| **useChartActions** | hooks/useChartActions.ts | 图表操作上下文 | 内置状态管理 |
| **useStatisticsActions** | hooks/useStatisticsActions.ts | 统计数据操作上下文 | 内置状态管理 |
| **useUnifiedDataLoader** | hooks/useUnifiedDataLoader.ts | 统一数据加载器 | 策略集成 |
| **useInitializationContext** | hooks/useInitializationContext.ts | 组件初始化管理 | 状态协调 |

### 核心组件清单

| 组件名称 | 文件路径 | 主要职责 | 架构角色 |
|----------|----------|----------|----------|
| **UserOperationStatisticsDashboard** | UserOperationStatisticsDashboard.vue | 主容器组件 | 上下文提供者 |
| **FilterPanel** | components/FilterPanel.vue | 全局筛选器 | 参数管理 |
| **LocalFilterPanel** | components/LocalFilterPanel.vue | 局部筛选器 | 组参数管理 |
| **ChartGroup** | components/ChartGroup.vue | 图表组容器 | 上下文消费者 |
| **EChartsComponent** | components/EChartsComponent.vue | 图表渲染组件 | 数据展示 |

### 策略系统清单

| 策略类型 | 文件路径 | 主要职责 | 支持功能 |
|----------|----------|----------|----------|
| **BaseStrategy** | strategies/BaseStrategy.ts | 策略基类 | 参数获取、数据加载 |
| **ChartDataLoadingStrategy** | strategies/ChartDataLoadingStrategy.ts | 图表数据策略 | 图表数据加载 |
| **StatisticsDataLoadingStrategy** | strategies/StatisticsDataLoadingStrategy.ts | 统计数据策略 | 统计数据加载 |
| **StrategyDependencyContainer** | strategies/StrategyDependencyInjection.ts | 依赖注入容器 | 依赖管理 |

### 架构完整性验证

✅ **参数管理系统**：统一查询参数管理器 + 全局/局部筛选器
✅ **配置管理系统**：Tab配置管理器 + 图表配置管理
✅ **数据管理系统**：全局数据源管理器 + 统一数据加载器
✅ **上下文系统**：图表/统计操作上下文 + 内置状态管理
✅ **策略系统**：策略基类 + 具体策略实现 + 依赖注入
✅ **组件系统**：主容器 + 筛选器 + 图表组件 + 数据展示

---

**总结**：统计看板核心架构通过统一查询参数管理器、依赖注入容器、策略模式和上下文系统的有机结合，实现了高度解耦、易扩展、高性能的现代化前端架构。特别是移除API装饰器后，架构变得更加简洁高效，策略类直接通过查询参数管理器获取参数，简化了调用链路，提升了系统的可维护性和性能。整个架构涵盖了从参数管理到数据展示的完整链路，为统计看板提供了坚实的技术基础。


