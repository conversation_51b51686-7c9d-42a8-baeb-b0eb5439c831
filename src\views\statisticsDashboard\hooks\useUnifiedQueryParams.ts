/**
 * 统一查询参数管理器
 * 实现全局参数和局部参数的统一管理，明确作用域隔离
 */

import { provide, inject, reactive, ref, type InjectionKey } from 'vue';
import type { commonQueryParams } from '../api';
import { StatisticalDimensionTypeEnum } from '../enums';
import { useUserStore } from '/@/store/modules/user';

/**
 * 参数作用域枚举
 */
export enum ParamScope {
  GLOBAL = 'global',
  GROUP = 'group',
}

/**
 * 统一查询参数管理器接口
 */
export interface UnifiedQueryParamsManager {
  /** 全局参数 */
  globalParams: Partial<commonQueryParams>;

  /** 组级别参数映射 */
  groupParams: Record<string, Partial<commonQueryParams>>;

  /** 设置全局参数 */
  setGlobalParams: (params: Partial<commonQueryParams>) => void;

  /** 更新全局参数（合并） */
  updateGlobalParams: (params: Partial<commonQueryParams>) => void;

  /** 设置组参数 */
  setGroupParams: (groupId: string, params: Partial<commonQueryParams>) => void;

  /** 更新组参数（合并） */
  updateGroupParams: (groupId: string, params: Partial<commonQueryParams>) => void;

  /** 获取最终参数 - 根据作用域返回对应参数 */
  getFinalParams: (groupId?: string) => commonQueryParams;

  /** 获取参数作用域 */
  getParamScope: (groupId?: string) => ParamScope;

  /** 清除组参数 */
  clearGroupParams: (groupId: string) => void;

  /** 清除所有组参数 */
  clearAllGroupParams: () => void;

  /** 重置全局参数为默认值 */
  resetGlobalParams: () => void;

  /** 参数变化监听 */
  onParamsChange: (callback: (params: commonQueryParams, scope: ParamScope, groupId?: string) => void) => void;

  /** 获取默认参数 */
  getDefaultParams: () => commonQueryParams;

  /** 完成初始化 */
  finishInitialization: () => void;
}

/**
 * 统一查询参数上下文键
 */
export const UNIFIED_QUERY_PARAMS_CONTEXT_KEY: InjectionKey<UnifiedQueryParamsManager> = Symbol('unifiedQueryParamsContext');

/**
 * 获取默认查询参数
 */
function getDefaultQueryParams(): commonQueryParams {
  const { userInfo } = useUserStore();

  const today = new Date().toISOString().split('T')[0];

  return {
    startDate: today,
    endDate: today,
    statisticalDimension: StatisticalDimensionTypeEnum.DAY,
    regionCenterCode: userInfo?.regionCenterCode || '',
  } as commonQueryParams;
}

/**
 * 提供统一查询参数管理器
 */
export function provideUnifiedQueryParams(): UnifiedQueryParamsManager {
  // 全局参数
  const globalParams = reactive<Partial<commonQueryParams>>(getDefaultQueryParams());

  // 组级别参数映射
  const groupParams = reactive<Record<string, Partial<commonQueryParams>>>({});

  // 参数变化监听器
  const changeListeners = ref<Array<(params: commonQueryParams, scope: ParamScope, groupId?: string) => void>>([]);

  // 初始化状态标记
  const isInitializing = ref(true);

  // 防抖定时器
  const debounceTimers = new Map<string, NodeJS.Timeout>();

  /**
   * 触发参数变化事件（带防抖和初始化状态检查）
   */
  const triggerParamsChange = (params: commonQueryParams, scope: ParamScope, groupId?: string, immediate = false) => {
    // 如果正在初始化且不是立即触发，则跳过
    if (isInitializing.value && !immediate) {
      console.log('🔄 初始化期间跳过参数变化触发:', scope, groupId);
      return;
    }

    const key = `${scope}_${groupId || 'global'}`;

    // 清除之前的防抖定时器
    if (debounceTimers.has(key)) {
      clearTimeout(debounceTimers.get(key)!);
    }

    // 设置新的防抖定时器
    const timer = setTimeout(
      () => {
        changeListeners.value.forEach((callback) => {
          try {
            callback(params, scope, groupId);
          } catch (error) {
            console.error('参数变化监听器执行失败:', error);
          }
        });
        debounceTimers.delete(key);
      },
      immediate ? 0 : 300
    ); // 300ms防抖，立即触发时为0

    debounceTimers.set(key, timer);
  };

  /**
   * 设置全局参数
   */
  const setGlobalParams = (params: Partial<commonQueryParams>) => {
    Object.assign(globalParams, params);
    const finalParams = getFinalParams();
    console.log('🌍 全局参数已更新:', params, '最终参数:', finalParams);
    triggerParamsChange(finalParams, ParamScope.GLOBAL);
  };

  /**
   * 更新全局参数（合并）
   */
  const updateGlobalParams = (params: Partial<commonQueryParams>) => {
    Object.assign(globalParams, params);
    const finalParams = getFinalParams();
    console.log('🌍 全局参数已合并更新:', params, '最终参数:', finalParams);
    triggerParamsChange(finalParams, ParamScope.GLOBAL);
  };

  /**
   * 设置组参数
   */
  const setGroupParams = (groupId: string, params: Partial<commonQueryParams>) => {
    groupParams[groupId] = { ...params };
    const finalParams = getFinalParams(groupId);
    console.log(`🏷️ 组参数已设置 [${groupId}]:`, params, '最终参数:', finalParams);
    triggerParamsChange(finalParams, ParamScope.GROUP, groupId);
  };

  /**
   * 更新组参数（合并）
   */
  const updateGroupParams = (groupId: string, params: Partial<commonQueryParams>) => {
    if (!groupParams[groupId]) {
      groupParams[groupId] = {};
    }
    Object.assign(groupParams[groupId], params);
    const finalParams = getFinalParams(groupId);
    console.log(`🏷️ 组参数已合并更新 [${groupId}]:`, params, '最终参数:', finalParams);
    triggerParamsChange(finalParams, ParamScope.GROUP, groupId);
  };

  /**
   * 获取参数作用域
   */
  const getParamScope = (groupId?: string): ParamScope => {
    return groupId && groupParams[groupId] ? ParamScope.GROUP : ParamScope.GLOBAL;
  };

  /**
   * 获取最终参数 - 根据作用域返回对应参数
   * 业务规则：
   * - 如果有 groupId 且存在组参数，则只使用组参数（局部筛选器完全独立）
   * - 否则使用全局参数
   */
  const getFinalParams = (groupId?: string): commonQueryParams => {
    const scope = getParamScope(groupId);

    if (scope === ParamScope.GROUP && groupId) {
      // 局部筛选器作用域：只使用组参数
      const params = {
        ...getDefaultQueryParams(),
        ...groupParams[groupId],
      } as commonQueryParams;

      console.log(`🎯 获取组参数 [${groupId}]:`, params);
      console.log(`🎯 当前组参数映射:`, groupParams);
      return params;
    } else {
      // 全局筛选器作用域：使用全局参数
      const params = {
        ...getDefaultQueryParams(),
        ...globalParams,
      } as commonQueryParams;

      console.log('🌍 获取全局参数:', params);
      console.log('🌍 当前全局参数:', globalParams);
      return params;
    }
  };

  /**
   * 清除组参数
   */
  const clearGroupParams = (groupId: string) => {
    delete groupParams[groupId];
    console.log(`🗑️ 已清除组参数 [${groupId}]`);
  };

  /**
   * 清除所有组参数
   */
  const clearAllGroupParams = () => {
    Object.keys(groupParams).forEach((groupId) => {
      delete groupParams[groupId];
    });
    console.log('🗑️ 已清除所有组参数');
  };

  /**
   * 重置全局参数为默认值
   */
  const resetGlobalParams = () => {
    const defaultParams = getDefaultQueryParams();
    Object.assign(globalParams, defaultParams);
    const finalParams = getFinalParams();
    console.log('🔄 全局参数已重置为默认值:', finalParams);
    triggerParamsChange(finalParams, ParamScope.GLOBAL);
  };

  /**
   * 参数变化监听
   */
  const onParamsChange = (callback: (params: commonQueryParams, scope: ParamScope, groupId?: string) => void) => {
    changeListeners.value.push(callback);
  };

  /**
   * 获取默认参数
   */
  const getDefaultParams = (): commonQueryParams => {
    return getDefaultQueryParams();
  };

  /**
   * 完成初始化
   */
  const finishInitialization = () => {
    isInitializing.value = false;
    console.log('🎯 统一查询参数管理器初始化完成');
  };

  // 创建管理器对象
  const manager: UnifiedQueryParamsManager = {
    globalParams,
    groupParams,
    setGlobalParams,
    updateGlobalParams,
    setGroupParams,
    updateGroupParams,
    getFinalParams,
    getParamScope,
    clearGroupParams,
    clearAllGroupParams,
    resetGlobalParams,
    onParamsChange,
    getDefaultParams,
    finishInitialization,
  };

  // 提供上下文
  provide(UNIFIED_QUERY_PARAMS_CONTEXT_KEY, manager);

  return manager;
}

/**
 * 使用统一查询参数管理器
 */
export function useUnifiedQueryParams(): UnifiedQueryParamsManager {
  const manager = inject(UNIFIED_QUERY_PARAMS_CONTEXT_KEY);

  if (!manager) {
    throw new Error('useUnifiedQueryParams must be used within a component that provides UnifiedQueryParamsManager');
  }

  return manager;
}

/**
 * 可选的统一查询参数管理器（不抛出错误）
 */
export function useUnifiedQueryParamsOptional(): UnifiedQueryParamsManager | null {
  return inject(UNIFIED_QUERY_PARAMS_CONTEXT_KEY, null);
}

/**
 * 便捷函数：获取当前查询参数
 * 根据上下文自动判断作用域
 */
export function getCurrentUnifiedQueryParams(groupId?: string): commonQueryParams {
  const manager = useUnifiedQueryParamsOptional();

  if (manager) {
    return manager.getFinalParams(groupId);
  }

  // 降级处理：返回默认参数
  console.warn('🔥 统一查询参数管理器不可用，使用默认参数');
  return getDefaultQueryParams();
}
