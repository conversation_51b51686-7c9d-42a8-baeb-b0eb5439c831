/**
 * 统一数据加载管理器
 * 整合策略系统到现有架构，提供统一的图表数据加载接口
 */

import { reactive, inject } from 'vue';
import { message } from 'ant-design-vue';
import type { ChartConfig, ChartDataItem, StatisticsConfig, StatisticsItem } from '../types/statisticDashboard';

import { useTabConfigManager } from './useTabConfigManager';
import { chartDataLoadingStrategyFactory, type IChartDataLoadingStrategy } from '../strategies/ChartDataLoadingStrategy';
import { StatisticsDataLoadingStrategyFactory, type IStatisticsDataLoadingStrategy } from '../strategies/StatisticsDataLoadingStrategy';
import { STATISTICS_DATA_CONTEXT_KEY } from './useStatisticsActions';
import { awaitTo } from '@ruqi/utils-admin';

/**
 * 图表加载状态管理接口
 */
interface ChartLoadingState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  strategy: string | null;
}

/**
 * 统计数据加载状态管理接口
 */
interface StatisticsLoadingState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  strategy: string | null;
}

let globalUnifiedDataLoader: ReturnType<typeof createUnifiedDataLoader> | null = null;

/**
 * 统一数据加载管理器
 */
function createUnifiedDataLoader(statisticsDataContext?: any) {
  const tabConfigManager = useTabConfigManager();

  // 🔥 获取统计数据上下文：优先使用传入的参数，否则尝试inject
  const getStatisticsDataContext = () => {
    if (statisticsDataContext) {
      return statisticsDataContext;
    }

    try {
      return inject(STATISTICS_DATA_CONTEXT_KEY, null);
    } catch (error) {
      return null;
    }
  };

  // 图表加载状态管理
  const chartLoadingStates = reactive<Record<string, ChartLoadingState>>({});

  // 统计数据加载状态管理
  const statisticsLoadingStates = reactive<Record<string, StatisticsLoadingState>>({});

  // 策略缓存
  const chartStrategyCache = new Map<string, IChartDataLoadingStrategy>();
  const statisticsStrategyCache = new Map<string, IStatisticsDataLoadingStrategy>();

  /**
   * 设置图表加载状态
   */
  const setLoadingState = (chartId: string, loading: boolean, error: string | null = null, strategy?: string) => {
    if (!chartLoadingStates[chartId]) {
      chartLoadingStates[chartId] = {
        loading: false,
        error: null,
        lastUpdated: null,
        strategy: null,
      };
    }
    chartLoadingStates[chartId].loading = loading;
    chartLoadingStates[chartId].error = error;
    if (strategy) {
      chartLoadingStates[chartId].strategy = strategy;
    }

    if (!loading && !error) {
      chartLoadingStates[chartId].lastUpdated = new Date();
    }
  };

  /**
   * 获取或创建策略实例
   */
  const getStrategy = (chartConfig: ChartConfig): IChartDataLoadingStrategy => {
    return chartDataLoadingStrategyFactory.getStrategy(chartConfig);
  };

  /**
   * 获取统计数据加载策略
   */
  const getStatisticsStrategy = (statisticsConfig: StatisticsConfig): IStatisticsDataLoadingStrategy => {
    return StatisticsDataLoadingStrategyFactory.getStrategy(statisticsConfig);
  };

  /**
   * 统一加载图表数据
   * 🔥 核心方法：通过策略自动获取筛选参数
   */
  const loadChartData = async (chartId: string): Promise<ChartDataItem[]> => {
    try {
      const chartConfig = tabConfigManager.getChartConfig(chartId);
      if (!chartConfig) {
        throw new Error(`图表配置未找到: ${chartId}`);
      }

      // 获取策略
      const strategy = getStrategy(chartConfig);

      // 🔥 获取图表所属的组ID，用于局部参数合并
      const groupId = tabConfigManager.getGroupIdByChartIdOrStatisticsId(chartId);
      const context = groupId ? { groupId } : undefined;

      // 设置加载状态
      setLoadingState(chartId, true, null, strategy.strategyType);

      const [error, data] = await awaitTo(strategy.loadData(chartConfig, context));

      // 更新图表配置,如果图表数据加载失败，把data更新为空
      await updateChartConfigWithData(chartConfig, error ? [] : data);

      // 清除加载状态
      setLoadingState(chartId, false);

      return error ? [] : data;
    } catch (error) {
      console.log('加载数据出错', error);
      setLoadingState(chartId, false);
      return [];
    }
  };

  /**
   * 刷新图表数据
   */
  const refreshChartData = async (chartId: string): Promise<ChartDataItem[]> => {
    try {
      const chartConfig = tabConfigManager.getChartConfig(chartId);
      if (!chartConfig) {
        throw new Error(`图表配置未找到: ${chartId}`);
      }

      const strategy = getStrategy(chartConfig);

      // 获取 groupId 上下文
      const groupId = tabConfigManager.getGroupIdByChartIdOrStatisticsId(chartId);
      const context = groupId ? { groupId } : undefined;

      setLoadingState(chartId, true, null, strategy.strategyType);

      const [error, data] = await awaitTo(strategy.refreshData(chartConfig, context));

      await updateChartConfigWithData(chartConfig, error ? [] : data);

      setLoadingState(chartId, false);

      console.log(`🔄 图表数据刷新成功: ${chartId}`);
      return error ? [] : data;
    } catch (error) {
      console.log('加载数据失败', error);
      setLoadingState(chartId, false);
      return [];
    }
  };

  /**
   * 刷新组数据
   * @param groupId
   * @param customParams
   * @returns
   */
  const refreshGroupData = async (groupId: string) => {
    const { group, tabId } = tabConfigManager.getGroupConfig(groupId);
    // 刷新组里图表数据、统计数据
    if (!group) return;

    tabConfigManager.setTabLoading(tabId, true);

    const promises: Promise<ChartDataItem[] | StatisticsItem[]>[] = [];

    group.chartList?.forEach((chart) => {
      promises.push(refreshChartData(chart.id));
    });

    if (group.statisticsConfig) {
      promises.push(refreshStatisticsData(group.statisticsConfig.id));
    }
    await Promise.all(promises);
    tabConfigManager.setTabLoading(tabId, false);
    console.log('刷新组数据成功', tabId);
  };

  /**
   * 切换数据源并重新加载
   */
  const switchDataSourceAndReload = async (chartId: string, newDataSource: string) => {
    const chartConfig = tabConfigManager.getChartConfig(chartId);
    if (!chartConfig) {
      throw new Error(`图表配置未找到: ${chartId}`);
    }

    const strategy = getStrategy(chartConfig);

    // 检查策略是否支持数据源切换
    if (!strategy.switchDataSource || !strategy.getDataSourceSwitchInfo) {
      console.warn(`策略 ${strategy.strategyType} 不支持数据源切换`);
      return await loadChartData(chartId);
    }

    setLoadingState(chartId, true, null, strategy.strategyType);

    console.log(`🔄 切换数据源: ${chartId} -> ${newDataSource}`);

    const [error, data] = await awaitTo(strategy.switchDataSource(chartConfig, newDataSource));

    // 策略支持获取切换信息，使用策略方法
    const switchInfo = await strategy.getDataSourceSwitchInfo(
      chartConfig.dataSource || chartConfig.customProps?.currentDataSource || '',
      newDataSource
    );
    const newTitle = switchInfo.newTitle;
    const alternativeTitle = switchInfo.alternativeTitle;
    const alternativeDataSource = switchInfo.alternativeDataSource;
    console.log(`🎯 使用策略获取标题信息:`, switchInfo);

    // 先更新数据源配置和标题
    tabConfigManager.updateChartConfig(chartId, {
      title: newTitle,
      dataSource: newDataSource,
      customProps: {
        ...chartConfig.customProps,
        currentDataSource: newDataSource,
        alternativeDataSource: alternativeDataSource,
        alternativeTitle: alternativeTitle,
        loading: true,
        needsAsyncData: true,
      },
    });
    await updateChartConfigWithData(chartConfig, error ? [] : data, newDataSource);

    setLoadingState(chartId, false);

    console.log(`✅ 数据源切换成功: ${chartId} -> ${newDataSource}`);
  };

  /**
   * 批量加载所有异步图表
   * 🔥 自动获取筛选参数并并发加载
   */
  const loadAllAsyncCharts = async () => {
    const allTabs = tabConfigManager.getAllTabs();
    const asyncCharts: ChartConfig[] = [];

    // 收集所有需要异步加载的图表
    for (const tab of allTabs) {
      for (const group of tab.groups || []) {
        for (const chart of group.chartList || []) {
          if (chart.customProps?.needsAsyncData) {
            asyncCharts.push(chart);
          }
        }
      }
    }

    if (asyncCharts.length === 0) {
      console.log('📋 没有需要异步加载的图表');
      return;
    }

    console.log(`🚀 开始批量加载 ${asyncCharts.length} 个异步图表`);

    // 🔥 并发加载所有图表，自动注入筛选参数
    const loadPromises = asyncCharts.map((chart) =>
      loadChartData(chart.id).catch((error) => {
        console.error(`图表 ${chart.id} 加载失败:`, error);
        return null;
      })
    );

    try {
      const results = await Promise.allSettled(loadPromises);
      const successful = results.filter((result) => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      // if (failed === 0) {
      //   message.success(`所有图表数据加载完成 (${successful}个)`);
      // } else {
      //   message.warning(`图表数据加载完成，${successful}个成功，${failed}个失败`);
      // }

      console.log(`✅ 批量加载完成: ${successful}成功 / ${failed}失败`);
    } catch (error) {
      console.error('批量加载过程中发生错误:', error);
      message.error('批量加载过程中发生错误');
    }
  };

  /**
   * 批量刷新所有异步图表
   */
  const refreshAllAsyncCharts = async () => {
    return await loadAllAsyncCharts();
  };

  /**
   * 更新图表配置数据
   * 🔥 通过策略统一处理图表配置更新
   */
  const updateChartConfigWithData = async (chartConfig: ChartConfig, data: ChartDataItem[], newDataSource?: string) => {
    try {
      // 使用策略模式统一处理图表配置更新
      const strategy = chartDataLoadingStrategyFactory.getStrategy(chartConfig);

      if (!strategy) {
        console.warn(`⚠️ 未找到对应策略，跳过配置更新: ${chartConfig.id}`);
        return;
      }

      // 通过策略更新图表配置
      const updateConfig = await strategy.updateChartConfig(chartConfig, data, newDataSource);
      tabConfigManager.updateChartConfig(chartConfig.id, updateConfig);
      console.log(`📊 图表配置更新完成: ${chartConfig.id} (策略: ${strategy.strategyType})`);
    } catch (error) {
      console.error('更新图表配置失败:', error);
      // 不抛出错误，避免影响数据加载流程
    }
  };

  /**
   * 获取图表加载状态
   */
  const getLoadingState = (chartId: string): ChartLoadingState => {
    return (
      chartLoadingStates[chartId] || {
        loading: false,
        error: null,
        lastUpdated: null,
        strategy: null,
      }
    );
  };

  /**
   * 获取所有加载状态
   */
  const getAllLoadingStates = () => {
    return { ...chartLoadingStates };
  };

  /**
   * 清理资源
   */
  const cleanup = () => {
    chartStrategyCache.clear();
    statisticsStrategyCache.clear();
    Object.keys(chartLoadingStates).forEach((key) => delete chartLoadingStates[key]);
    Object.keys(statisticsLoadingStates).forEach((key) => delete statisticsLoadingStates[key]);
  };

  // ========== 统计数据加载方法 ==========

  /**
   * 设置统计数据加载状态
   */
  const setStatisticsLoadingState = (configId: string, loading: boolean, error: string | null = null, strategy?: string) => {
    if (!statisticsLoadingStates[configId]) {
      statisticsLoadingStates[configId] = {
        loading: false,
        error: null,
        lastUpdated: null,
        strategy: null,
      };
    }

    statisticsLoadingStates[configId].loading = loading;
    statisticsLoadingStates[configId].error = error;
    if (strategy) {
      statisticsLoadingStates[configId].strategy = strategy;
    }
    if (!loading) {
      statisticsLoadingStates[configId].lastUpdated = new Date();
    }
  };

  /**
   * 加载统计数据
   */
  const loadStatisticsData = async (config: StatisticsConfig): Promise<StatisticsItem[]> => {
    const configId = config.id;

    try {
      setStatisticsLoadingState(configId, true);

      // 获取或创建策略
      const strategy = getStatisticsStrategy(config);
      const groupId = tabConfigManager.getGroupIdByChartIdOrStatisticsId(configId);

      const context = groupId ? { groupId } : undefined;

      // 加载数据
      const statisticsData = await strategy.loadData(config, context);

      // 更新配置管理器中的统计数据
      tabConfigManager.updateStatisticsData(configId, statisticsData);

      // 🔥 延迟获取并更新统计数据上下文（避免循环依赖）
      const statisticsDataContext = getStatisticsDataContext();

      if (statisticsDataContext) {
        statisticsDataContext.setStatisticsData(configId, statisticsData);
      }

      setStatisticsLoadingState(configId, false, null, strategy.strategyType);
      console.log(`✅ 统计数据加载成功: ${configId}, 数据条数: ${statisticsData.length}`);

      return statisticsData;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setStatisticsLoadingState(configId, false, errorMessage);
      console.error(`❌ 统计数据加载失败: ${configId}`, error);
      throw error;
    }
  };

  /**
   * 刷新统计数据
   */
  const refreshStatisticsData = async (configId: string): Promise<StatisticsItem[]> => {
    const config = tabConfigManager.getStatisticsConfig(configId);
    if (!config) {
      throw new Error(`未找到统计配置: ${configId}`);
    }

    return loadStatisticsData(config);
  };

  /**
   * 批量加载所有需要异步数据的统计配置
   */
  const loadAllAsyncStatistics = async (): Promise<void> => {
    const allStatisticsConfigs = tabConfigManager.getAllStatisticsConfigs();
    const asyncConfigs = allStatisticsConfigs.filter((config) => config.needsAsyncData);

    if (asyncConfigs.length === 0) {
      console.log('📊 没有需要异步加载的统计配置');
      return;
    }

    console.log(`📊 开始批量加载 ${asyncConfigs.length} 个统计配置`);

    // 并发加载所有统计数据
    const loadPromises = asyncConfigs.map((config) =>
      loadStatisticsData(config).catch((error) => {
        console.error(`统计数据加载失败: ${config.id}`, error);
        return []; // 返回空数组，不影响其他加载
      })
    );

    await Promise.all(loadPromises);
    console.log('✅ 批量统计数据加载完成');
  };

  /**
   * 批量刷新所有需要异步数据的统计配置
   */
  const refreshAllAsyncStatistics = async (): Promise<void> => {
    return await loadAllAsyncStatistics();
  };

  /**
   * 获取统计数据加载状态
   */
  const getStatisticsLoadingState = (configId: string): StatisticsLoadingState | null => {
    return statisticsLoadingStates[configId] || null;
  };

  /**
   * 获取所有统计数据加载状态
   */
  const getAllStatisticsLoadingStates = (): Record<string, StatisticsLoadingState> => {
    return { ...statisticsLoadingStates };
  };

  return {
    // 图表数据方法 - 🔥 自动注入筛选参数
    loadChartData,
    refreshChartData,
    switchDataSourceAndReload,
    loadAllAsyncCharts,
    refreshAllAsyncCharts,
    refreshGroupData,
    // 🔥 新增：统计数据方法
    loadStatisticsData,
    refreshStatisticsData,
    loadAllAsyncStatistics,
    refreshAllAsyncStatistics,

    // 状态管理
    getLoadingState,
    getAllLoadingStates,
    loadingStates: chartLoadingStates,

    // 🔥 新增：统计数据状态管理
    getStatisticsLoadingState,
    getAllStatisticsLoadingStates,
    statisticsLoadingStates,

    // 工具方法
    cleanup,

    // 内部方法暴露（用于调试和扩展）
    getStrategy,
    updateChartConfigWithData,
  };
}

/**
 * 获取全局数据加载器
 */
export function useUnifiedDataLoader(statisticsDataContext?: any) {
  if (!globalUnifiedDataLoader) {
    globalUnifiedDataLoader = createUnifiedDataLoader(statisticsDataContext);
  }
  return globalUnifiedDataLoader;
}
