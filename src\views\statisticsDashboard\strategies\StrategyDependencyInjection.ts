/**
 * 策略依赖注入配置
 * 统一管理策略实例的依赖注入，确保所有策略共享同一个数据源管理器实例
 */

import { useGlobalDataSourceManager, type IDataSourceManager } from '../hooks/useDataSourceManager';
import type { UnifiedQueryParamsManager } from '../hooks/useUnifiedQueryParams';
import type { IChartDataLoadingStrategy } from './ChartDataLoadingStrategy';
import type { IStatisticsDataLoadingStrategy } from './StatisticsDataLoadingStrategy';
import type { IChartDrillDownStrategy } from './ChartDrillDownStrategy';

/**
 * 策略依赖注入容器
 */
export class StrategyDependencyContainer {
  private static instance: StrategyDependencyContainer;
  private dataSourceManager: IDataSourceManager;
  private queryParamsManager?: UnifiedQueryParamsManager;

  private constructor() {
    // 使用全局单例数据源管理器
    this.dataSourceManager = useGlobalDataSourceManager();
    console.log('🏗️ 策略依赖注入容器初始化完成');
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): StrategyDependencyContainer {
    if (!StrategyDependencyContainer.instance) {
      StrategyDependencyContainer.instance = new StrategyDependencyContainer();
    }
    return StrategyDependencyContainer.instance;
  }

  /**
   * 获取数据源管理器实例
   */
  public getDataSourceManager(): IDataSourceManager {
    return this.dataSourceManager;
  }

  /**
   * 设置查询参数管理器
   */
  public setQueryParamsManager(manager: UnifiedQueryParamsManager): void {
    this.queryParamsManager = manager;
    console.log('🔧 查询参数管理器已注入到策略依赖容器');
  }

  /**
   * 获取查询参数管理器
   */
  public getQueryParamsManager(): UnifiedQueryParamsManager | undefined {
    return this.queryParamsManager;
  }

  /**
   * 创建图表数据加载策略实例（带依赖注入）
   */
  public createChartDataLoadingStrategy<T extends IChartDataLoadingStrategy>(StrategyClass: new (dataSourceManager?: IDataSourceManager) => T): T {
    // 创建策略实例，查询参数管理器会通过getQueryParamsManager方法获取
    return new StrategyClass(this.dataSourceManager);
  }

  /**
   * 创建统计数据加载策略实例（带依赖注入）
   */
  public createStatisticsDataLoadingStrategy<T extends IStatisticsDataLoadingStrategy>(
    StrategyClass: new (dataSourceManager?: IDataSourceManager) => T
  ): T {
    return new StrategyClass(this.dataSourceManager);
  }

  /**
   * 创建图表下探策略实例（带依赖注入）
   */
  public createChartDrillDownStrategy<T extends IChartDrillDownStrategy>(StrategyClass: new (dataSourceManager?: IDataSourceManager) => T): T {
    return new StrategyClass(this.dataSourceManager);
  }

  /**
   * 重置依赖注入容器（主要用于测试）
   */
  public static reset(): void {
    StrategyDependencyContainer.instance = undefined as any;
  }
}

/**
 * 获取策略依赖注入容器实例
 */
export function getStrategyDependencyContainer(): StrategyDependencyContainer {
  return StrategyDependencyContainer.getInstance();
}

/**
 * 便捷方法：获取全局数据源管理器
 */
export function getGlobalDataSourceManager(): IDataSourceManager {
  return getStrategyDependencyContainer().getDataSourceManager();
}

/**
 * 便捷方法：创建带依赖注入的图表数据加载策略
 */
export function createChartDataLoadingStrategy<T extends IChartDataLoadingStrategy>(
  StrategyClass: new (dataSourceManager?: IDataSourceManager) => T
): T {
  return getStrategyDependencyContainer().createChartDataLoadingStrategy(StrategyClass);
}

/**
 * 便捷方法：创建带依赖注入的统计数据加载策略
 */
export function createStatisticsDataLoadingStrategy<T extends IStatisticsDataLoadingStrategy>(
  StrategyClass: new (dataSourceManager?: IDataSourceManager) => T
): T {
  return getStrategyDependencyContainer().createStatisticsDataLoadingStrategy(StrategyClass);
}

/**
 * 便捷方法：创建带依赖注入的图表下探策略
 */
export function createChartDrillDownStrategy<T extends IChartDrillDownStrategy>(StrategyClass: new (dataSourceManager?: IDataSourceManager) => T): T {
  return getStrategyDependencyContainer().createChartDrillDownStrategy(StrategyClass);
}
