/**
 * 图表布局工具函数
 * 提供布局配置的默认值处理和验证功能
 */

import type { ChartLayoutConfig } from '../types/statisticDashboard';

/**
 * 默认布局配置
 */
export const DEFAULT_LAYOUT_CONFIG = {
  columnSpan: 1,
  maxWidth: undefined as string | undefined,
  minWidth: undefined as string | undefined,
  centerOnWideScreen: false,
};

/**
 * 获取标准化的布局配置
 * @param layout 用户提供的布局配置
 * @returns 标准化后的布局配置
 */
export function normalizeLayoutConfig(layout?: ChartLayoutConfig) {
  if (!layout) {
    return DEFAULT_LAYOUT_CONFIG;
  }

  return {
    columnSpan: validateColumnSpan(layout.columnSpan),
    maxWidth: layout.maxWidth ?? undefined,
    minWidth: layout.minWidth ?? undefined,
    centerOnWideScreen: layout.centerOnWideScreen ?? DEFAULT_LAYOUT_CONFIG.centerOnWideScreen,
  };
}

/**
 * 验证并标准化列跨越值
 * @param columnSpan 列跨越值
 * @returns 标准化后的列跨越值（1-4之间）
 */
export function validateColumnSpan(columnSpan?: number): number {
  if (typeof columnSpan !== 'number' || columnSpan < 1) {
    return DEFAULT_LAYOUT_CONFIG.columnSpan;
  }
  return Math.min(Math.max(Math.floor(columnSpan), 1), 4);
}

/**
 * 生成网格列样式
 * @param layout 布局配置
 * @returns CSS样式对象
 */
export function generateGridColumnStyle(layout?: ChartLayoutConfig): Record<string, string> {
  const normalizedLayout = normalizeLayoutConfig(layout);
  const styles: Record<string, string> = {};

  // 设置网格列跨越
  if (normalizedLayout.columnSpan > 1) {
    styles.gridColumn = `span ${normalizedLayout.columnSpan}`;
  }

  // 设置最大宽度
  if (normalizedLayout.maxWidth) {
    styles.maxWidth = normalizedLayout.maxWidth;
  }

  // 设置最小宽度
  if (normalizedLayout.minWidth) {
    styles.minWidth = normalizedLayout.minWidth;
  }

  // 宽屏居中显示
  if (normalizedLayout.centerOnWideScreen) {
    styles.justifySelf = 'center';
  }
  return styles;
}

/**
 * 布局配置预设
 */
export const LAYOUT_PRESETS = {
  /** 默认单列 */
  default: {
    columnSpan: 1,
  } as ChartLayoutConfig,

  /** 双列跨越 */
  wide: {
    columnSpan: 2,
  } as ChartLayoutConfig,

  /** 全宽跨越 */
  fullWidth: {
    columnSpan: 4,
  } as ChartLayoutConfig,

  /** 居中显示（适合单个重要图表） */
  centered: {
    columnSpan: 2,
    maxWidth: '800px',
    centerOnWideScreen: true,
  } as ChartLayoutConfig,

  /** 紧凑布局（适合小图表） */
  compact: {
    columnSpan: 1,
    maxWidth: '500px',
  } as ChartLayoutConfig,
} as const;

/**
 * 获取预设布局配置
 * @param presetName 预设名称
 * @returns 布局配置
 */
export function getLayoutPreset(presetName: keyof typeof LAYOUT_PRESETS): ChartLayoutConfig {
  return LAYOUT_PRESETS[presetName];
}
