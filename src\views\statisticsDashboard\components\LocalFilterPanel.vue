<template>
  <BasicForm @register="registerForm" @field-value-change="_handleFieldChange" />
</template>

<script lang="ts">
  export default {
    name: 'LocalFilterPanel',
  };
</script>

<script setup lang="ts">
  import { computed, onMounted, nextTick } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form';
  import type { FormSchema } from '/@/components/Form';
  import type { LocalFilterConfig } from '../types/statisticDashboard';
  import { useFilters } from '../hooks/useFilters';
  import { useUnifiedQueryParams } from '../hooks/useUnifiedQueryParams';
  import { useComponentInitialization } from '../hooks/useInitializationContext';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { transformValuesToQueryParams, validateDateRange } from '../utils/filterUtils';
  import { useUnifiedDataLoader } from '../hooks/useUnifiedDataLoader';

  interface Props {
    config: LocalFilterConfig;
    modelValue?: Record<string, any>;
    groupId: string; // 从父组件传入的组ID
  }

  const props = defineProps<Props>();
  const unifiedDataLoader = useUnifiedDataLoader();

  // 使用与 FilterPanel 一致的 useFilters hook
  const {
    formSchemas: allFormSchemas,
    initializationPromise,
    adjustDateRangeByPeriod,
    getPeriodRangeDefaultValue,
    currentStatPeriod,
  } = useFilters({
    enableUserDataAuth: false,
    type: 'local',
  });

  // 使用统一查询参数管理器
  const unifiedQueryParams = useUnifiedQueryParams();

  // 使用组件初始化管理
  const componentInit = useComponentInitialization(`local-filter-panel-${props.groupId}`);

  // 根据配置生成表单字段，并对局部筛选器进行特殊处理
  const formSchemas = computed<FormSchema[]>(() => {
    return allFormSchemas.value
      .filter((schema) => {
        return props.config.fields.includes(schema.field as any);
      })
      .map((schema) => {
        schema.colProps = { span: 8 };
        // 对统计周期字段进行特殊处理：移除天、周选项
        if (schema.field === 'statPeriod') {
          return {
            ...schema,
            componentProps: {
              ...schema.componentProps,
              options: (schema.componentProps as any)?.options?.filter((option: any) => !['day', 'week'].includes(option.value)) || [],
            },
          };
        }
        return {
          ...schema,
        };
      });
  });

  // 使用与 FilterPanel 一致的 useForm 模式
  const [registerForm, formMethods] = useForm({
    schemas: formSchemas,
    showActionButtonGroup: false, // 局部筛选器不显示操作按钮
    layout: 'vertical',
    // 设置默认五列布局
    baseColProps: {
      span: 4, // 24 / 6 = 4，实现五列布局
      xs: 24, // 超小屏幕一列
      sm: 12, // 小屏幕两列
      md: 8, // 中等屏幕三列
      lg: 6, // 大屏幕四列
      xl: 4, // 超大屏幕五列
      xxl: 4, // 超超大屏幕五列
    },
  });

  const { setFieldsValue, getFieldsValue } = formMethods;

  // 处理字段变化（添加校验逻辑）
  const _handleFieldChange = async (field: string, value: string) => {
    let localParams = {} as Record<string, any>;
    // 执行默认的处理逻辑
    if (field === 'statPeriod' && value) {
      // 更新当前统计周期
      currentStatPeriod.value = value;
      const defaultRange = getPeriodRangeDefaultValue(value);
      // 设置默认值
      setFieldsValue({
        periodRange: defaultRange,
      });
      await nextTick();
      const values = getFieldsValue();
      localParams = transformValuesToQueryParams(
        {
          ...values,
          periodRange: defaultRange,
        },
        adjustDateRangeByPeriod
      );
      unifiedQueryParams.updateGroupParams(props.groupId, localParams);
      unifiedDataLoader.refreshGroupData(props.groupId);
      return;
    }

    // 单独处理
    if (field === 'periodRange') {
      const values = getFieldsValue();
      const validation = validateDateRange(values);
      if (!validation.valid) {
        // 校验失败，赋值回原来的值
        localParams = unifiedQueryParams.getFinalParams(props.groupId);
        setFieldsValue({
          periodRange: [localParams.startDate, localParams.endDate],
        });
        message.error(validation.message);
        return;
      }
      localParams = transformValuesToQueryParams(values, adjustDateRangeByPeriod);
      unifiedQueryParams.updateGroupParams(props.groupId, localParams);
      unifiedDataLoader.refreshGroupData(props.groupId);
    }
  };

  // 获取局部筛选器的默认值（不依赖配置传入）
  const getLocalFilterDefaults = () => {
    const now = new Date();
    return {
      statPeriod: 'month',
      periodRange: [dayjs(new Date(now.getFullYear(), now.getMonth() - 2, 1)).format('YYYY-MM-DD'), dayjs(now).format('YYYY-MM-DD')],
    };
  };

  // 初始化表单数据
  const initFormData = async () => {
    // 等待 useFilters 初始化完成
    await initializationPromise;
    // 使用局部筛选器的默认值逻辑，而不是配置传入的默认值
    const defaultData = getLocalFilterDefaults();
    if (props.modelValue) {
      Object.assign(defaultData, props.modelValue);
    }

    const params = transformValuesToQueryParams(defaultData, adjustDateRangeByPeriod);
    // 初始化局部查询参数
    unifiedQueryParams.setGroupParams(props.groupId, params);

    await nextTick();
    setFieldsValue(defaultData);
  };

  // 组件挂载时初始化
  onMounted(async () => {
    componentInit.setLoading();

    try {
      // 等待筛选器基础数据初始化完成
      await initializationPromise;

      // 初始化表单数据
      initFormData();

      componentInit.setReady();
      console.log(`🎯 LocalFilterPanel [${props.groupId}] 初始化完成`);
    } catch (error) {
      console.error(`🚨 LocalFilterPanel [${props.groupId}] 初始化失败:`, error);
      componentInit.setError();
    }
  });
</script>
