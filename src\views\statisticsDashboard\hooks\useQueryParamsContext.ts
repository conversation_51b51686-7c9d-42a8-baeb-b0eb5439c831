/**
 * 查询参数上下文系统
 * 统一管理筛选器查询参数，供所有图表操作使用
 */

import { provide, inject, ref, type InjectionKey, type Ref } from 'vue';
import type { commonQueryParams } from '../api';
import { StatisticalDimensionTypeEnum } from '../enums';
import { useUserStore } from '/@/store/modules/user';

// 🔥 全局查询参数获取器，解决上下文不可用的问题
let globalQueryParamsGetter: (() => Partial<commonQueryParams>) | null = null;

/**
 * 设置全局查询参数获取器
 */
export function setGlobalQueryParamsGetter(getter: () => Partial<commonQueryParams>) {
  globalQueryParamsGetter = getter;
  console.log('🔥 全局查询参数获取器已设置');
}

/**
 * 获取全局查询参数
 */
function getGlobalQueryParams(): Partial<commonQueryParams> {
  if (globalQueryParamsGetter) {
    try {
      const params = globalQueryParamsGetter();
      // console.log('🔥 使用全局查询参数获取器:', params);
      return params;
    } catch (error) {
      console.warn('🔥 全局查询参数获取器执行失败:', error);
    }
  }

  console.warn('🔥 全局查询参数获取器未设置，使用默认参数');

  // 使用导入的依赖
  const { userInfo } = useUserStore();

  const defaultParams = {
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    statisticalDimension: StatisticalDimensionTypeEnum.DAY,
    regionCenterCode: userInfo?.regionCenterCode,
  };

  console.log('🔥 全局默认查询参数:', defaultParams);
  return defaultParams;
}

/**
 * 查询参数上下文接口
 */
export interface QueryParamsContext {
  /** 获取当前查询参数 */
  getCurrentQueryParams: () => Partial<commonQueryParams>;
  /** 设置查询参数获取器 */
  setQueryParamsGetter: (getter: () => Partial<commonQueryParams>) => void;
  /** 查询参数响应式引用 */
  queryParams: Ref<Partial<commonQueryParams>>;
  /** 更新查询参数 */
  updateQueryParams: (params: Partial<commonQueryParams>) => void;
}

/**
 * 查询参数上下文键
 */
export const QUERY_PARAMS_CONTEXT_KEY: InjectionKey<QueryParamsContext> = Symbol('queryParamsContext');

/**
 * 提供查询参数上下文
 */
export function provideQueryParamsContext() {
  // 查询参数获取器
  let queryParamsGetter: (() => Partial<commonQueryParams>) | null = null;

  // 响应式查询参数
  const queryParams = ref<Partial<commonQueryParams>>({});

  /**
   * 设置查询参数获取器
   */
  const setQueryParamsGetter = (getter: () => Partial<commonQueryParams>) => {
    queryParamsGetter = getter;
    console.log('🔥 查询参数获取器已设置');
  };

  /**
   * 获取当前查询参数
   */
  const getCurrentQueryParams = (): Partial<commonQueryParams> => {
    if (queryParamsGetter) {
      try {
        const params = queryParamsGetter();
        console.log('🔥 通过上下文获取查询参数:', params);
        return params;
      } catch (error) {
        console.warn('🔥 查询参数获取器执行失败:', error);
      }
    }

    console.warn('🔥 查询参数获取器未设置，使用默认参数');

    // 使用导入的依赖
    const { userInfo } = useUserStore();

    const defaultParams = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      statisticalDimension: StatisticalDimensionTypeEnum.DAY,
      regionCenterCode: userInfo?.regionCenterCode,
    };

    console.log('🔥 使用默认查询参数:', defaultParams);
    return defaultParams;
  };

  /**
   * 更新查询参数
   */
  const updateQueryParams = (params: Partial<commonQueryParams>) => {
    queryParams.value = params;
  };

  // 创建上下文对象
  const context: QueryParamsContext = {
    getCurrentQueryParams,
    setQueryParamsGetter,
    queryParams,
    updateQueryParams,
  };

  // 提供上下文
  provide(QUERY_PARAMS_CONTEXT_KEY, context);

  return context;
}

/**
 * 使用查询参数上下文
 */
export function useQueryParamsContext(): QueryParamsContext {
  const context = inject(QUERY_PARAMS_CONTEXT_KEY);

  if (!context) {
    throw new Error('useQueryParamsContext must be used within a component that provides QueryParamsContext');
  }

  return context;
}

/**
 * 可选的查询参数上下文（不抛出错误）
 */
export function useQueryParamsContextOptional(): QueryParamsContext | null {
  return inject(QUERY_PARAMS_CONTEXT_KEY, null);
}

/**
 * 便捷函数：获取当前查询参数
 */
export function getCurrentQueryParams(): Partial<commonQueryParams> {
  // 🔥 优先使用全局获取器
  if (globalQueryParamsGetter) {
    return getGlobalQueryParams();
  }

  // 尝试使用上下文
  const context = useQueryParamsContextOptional();

  if (context) {
    return context.getCurrentQueryParams();
  }

  console.warn('🔥 查询参数上下文和全局获取器都不可用，使用默认参数');

  // 使用导入的依赖
  const { userInfo } = useUserStore();

  return {
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    statisticalDimension: StatisticalDimensionTypeEnum.DAY,
    regionCenterCode: userInfo?.regionCenterCode,
  };
}
