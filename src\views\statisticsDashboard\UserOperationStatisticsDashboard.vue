<template>
  <div class="statistics-dashboard">
    <PageWrapper>
      <!-- 权限检查：当用户没有任何Tab权限时显示提示信息 -->
      <template v-if="tabs.length === 0">
        <div class="flex items-center justify-center min-h-[60vh] p-8">
          <a-result status="403" title="暂无访问权限" sub-title="您当前没有查看任何统计看板的权限，请联系管理员开通相关权限。">
            <template #icon>
              <Icon icon="ant-design:lock-outlined" class="text-6xl text-gray-400" />
            </template>
            <template #extra>
              <a-button @click="handleRefreshPermission">
                <Icon icon="ant-design:reload-outlined" />
                刷新页面
              </a-button>
            </template>
          </a-result>
        </div>
      </template>

      <!-- 有权限时显示正常内容 -->
      <template v-else>
        <!-- 筛选器面板 - 集成吸顶功能 -->
        <StickyFilterWrapper
          :extra-top-offset="0"
          :trigger-threshold="10"
          header-selector=".ant-layout-header"
          nav-selector=".ant-tabs-nav"
          @sticky-change="handleStickyChange"
          @dimensions-update="handleDimensionsUpdate"
        >
          <template #filter>
            <FilterPanel :auto-apply="false" />
          </template>
        </StickyFilterWrapper>

        <!-- Tab容器 -->
        <TabContainer v-model="activeTab" :tabs="tabs" @tab-change="handleTabChange" />
      </template>

      <!-- 浮动操作按钮 - 只在有权限时显示 -->
      <div v-if="tabs.length > 0" class="floating-actions">
        <a-float-button-group trigger="click" type="primary">
          <template #icon>
            <Icon icon="ant-design:setting-outlined" />
          </template>

          <a-float-button tooltip="刷新所有数据" @click="handleRefreshAll">
            <template #icon>
              <Icon icon="ant-design:reload-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="导出当前看板" @click="handleExportDashboard">
            <template #icon>
              <Icon icon="ant-design:download-outlined" />
            </template>
          </a-float-button>

          <a-float-button tooltip="看板设置" @click="handleDashboardSettings">
            <template #icon>
              <Icon icon="ant-design:control-outlined" />
            </template>
          </a-float-button>
        </a-float-button-group>
      </div>

      <!-- 设置抽屉 -->
      <a-drawer v-model:open="settingsVisible" title="看板设置" placement="right" width="400">
        <div class="settings-content">
          <a-form layout="vertical">
            <a-form-item label="显示设置">
              <a-space direction="vertical" style="width: 100%">
                <a-switch v-model:checked="isDraggingEnabled" checked-children="启用拖拽" un-checked-children="禁用拖拽" />
              </a-space>
            </a-form-item>
            <a-form-item label="主题设置">
              <a-radio-group v-model:value="theme">
                <a-radio value="light">浅色主题</a-radio>
                <a-radio value="dark">深色主题</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
      </a-drawer>
    </PageWrapper>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { Icon } from '/@/components/Icon';
  import FilterPanel from './components/FilterPanel.vue';
  import TabContainer from './components/TabContainer.vue';
  import StickyFilterWrapper from './components/StickyFilterWrapper.vue';
  // import PerformanceMonitor from './components/PerformanceMonitor.vue';
  import { useTabConfigManager, provideTabConfigContext } from './hooks/useTabConfigManager';
  import { provideChartActions, provideChartData, provideChartEvents, provideChartConfig } from './hooks/useChartActions';
  import { provideStatisticsActions, provideStatisticsData } from './hooks/useStatisticsActions';
  import { provideUnifiedQueryParams, ParamScope } from './hooks/useUnifiedQueryParams';
  import { provideInitializationContext } from './hooks/useInitializationContext';
  import { getStrategyDependencyContainer } from './strategies/StrategyDependencyInjection';
  import type { TabConfig } from './types/statisticDashboard';
  import { message } from 'ant-design-vue';
  import { useUnifiedDataLoader } from './hooks/useUnifiedDataLoader';
  import { multiModuleI18nManager } from '/@/utils/i18n/multiModuleI18nManager';

  const tabConfigManager = useTabConfigManager();
  const { availableTabs: tabs, activeTabId: activeTab } = tabConfigManager;

  // 提供Tab配置上下文
  provideTabConfigContext();

  // 提供统一查询参数管理器
  const _unifiedQueryParams = provideUnifiedQueryParams();

  // 提供初始化状态管理上下文
  const initContext = provideInitializationContext();

  // 注入查询参数管理器到策略依赖容器
  const strategyContainer = getStrategyDependencyContainer();
  strategyContainer.setQueryParamsManager(_unifiedQueryParams);

  // 本地状态
  const settingsVisible = ref(false);
  const isDraggingEnabled = ref(false);
  const theme = ref('light');

  // 1. 提供图表上下文（内置状态管理）
  const chartActions = provideChartActions();
  provideChartData(chartActions);
  provideChartEvents(chartActions.context.handleDrillDown);
  provideChartConfig();

  // 2. 提供统计数据上下文系统（内置状态管理）
  const statisticsActions = provideStatisticsActions();
  const statisticsDataContext = provideStatisticsData(statisticsActions);

  // 使用新的统一数据加载器 - 策略系统自动处理
  const unifiedLoader = useUnifiedDataLoader(statisticsDataContext);

  /**
   * 处理参数变化 - 统一的数据刷新逻辑
   */
  const handleParamsChange = async () => {
    // 检查权限
    if (tabs.value.length === 0) {
      console.log('🚫 用户没有权限，跳过参数变化处理');
      return;
    }

    try {
      // 设置当前Tab进入loading状态
      tabConfigManager.setTabLoading(activeTab.value, true);

      // 使用策略系统重新加载所有异步图表和统计数据
      try {
        await Promise.all([unifiedLoader.loadAllAsyncCharts(), unifiedLoader.loadAllAsyncStatistics()]);
        console.log('🎯 参数变化处理完成，数据已刷新');
      } finally {
        // 数据加载完成后清除Tab的loading状态
        tabConfigManager.setTabLoading(activeTab.value, false);
      }
    } catch (error) {
      console.error('🚨 参数变化处理失败:', error);
      // 发生错误时也要清除loading状态
      tabConfigManager.setTabLoading(activeTab.value, false);
    }
  };

  // 初始化完成后设置参数变化监听
  const setupParamsChangeListener = () => {
    _unifiedQueryParams.onParamsChange(async (params, scope, groupId) => {
      console.log(`🔄 参数变化监听触发 - 作用域: ${scope}${groupId ? `, 组ID: ${groupId}` : ''}`, params);

      // 只处理全局参数变化（局部参数变化由对应的组件自行处理）
      if (scope === ParamScope.GLOBAL) {
        await handleParamsChange();
      }
    });
  };

  /**
   * 处理Tab切换 - 只处理业务逻辑，Tab状态由上下文系统管理
   */
  const handleTabChange = (tabId: string, _tab: TabConfig) => {
    tabConfigManager.setActiveTab(tabId);
    // 注意：Tab状态已由TabContainer通过上下文系统设置，这里不需要重复设置
  };

  /**
   * 刷新所有数据 - 使用筛选器参数
   */
  const handleRefreshAll = async () => {
    // 检查权限
    if (tabs.value.length === 0) {
      console.log('🚫 用户没有权限，跳过数据刷新');
      message.warning('您没有权限执行此操作');
      return;
    }

    // 设置当前Tab进入loading状态
    tabConfigManager.setTabLoading(activeTab.value, true);

    try {
      // 使用策略系统刷新所有数据
      await Promise.all([unifiedLoader.refreshAllAsyncCharts(), unifiedLoader.refreshAllAsyncStatistics()]);

      // 清除缓存（使用统一数据加载器）
      unifiedLoader.cleanup();
    } catch (error) {
      console.error('刷新失败:', error);
    } finally {
      // 数据加载完成后清除Tab的loading状态
      tabConfigManager.setTabLoading(activeTab.value, false);
    }
  };

  /**
   * 处理看板导出
   */
  const handleExportDashboard = () => {
    message.info('正在导出整个看板...');
    // 这里实现看板导出逻辑
  };

  /**
   * 处理看板设置
   */
  const handleDashboardSettings = () => {
    settingsVisible.value = true;
  };

  /**
   * 处理刷新权限
   */
  const handleRefreshPermission = () => {
    // 刷新页面重新检查权限
    window.location.reload();
  };

  /**
   * 处理吸顶状态变化
   */
  const handleStickyChange = (_isSticky: boolean) => {
    // 可以在这里添加其他逻辑，比如：
    // - 调整页面布局
    // - 更新样式类
    // - 发送分析事件等
  };

  /**
   * 处理尺寸更新
   */
  const handleDimensionsUpdate = () => {
    // 可以在这里添加其他逻辑，比如：
    // - 调整其他组件的位置
    // - 更新全局样式变量
    // - 通知其他组件尺寸变化等
  };

  // 异步加载clue-vue模块的资源
  multiModuleI18nManager.loadModuleResources('clue-vue');

  // 组件挂载
  onMounted(async () => {
    // 检查是否有可用的Tab权限
    if (tabs.value.length === 0) {
      console.log('🚫 用户没有任何Tab访问权限，跳过数据初始化');
      return;
    }

    // 初始化默认激活Tab
    tabConfigManager.initializeActiveTab();

    try {
      // 等待所有组件初始化完成
      await initContext.waitForAllReady();
      console.log('🎯 所有组件初始化完成');

      // 完成统一查询参数管理器的初始化
      _unifiedQueryParams.finishInitialization();

      // 设置参数变化监听器
      setupParamsChangeListener();

      // 设置当前Tab进入loading状态
      tabConfigManager.setTabLoading(activeTab.value, true);

      // 触发初始数据加载（图表和统计数据）
      try {
        await Promise.all([unifiedLoader.loadAllAsyncCharts(), unifiedLoader.loadAllAsyncStatistics()]);
        console.log('🎯 初始数据加载完成');
      } finally {
        tabConfigManager.setTabLoading(activeTab.value, false);
      }
    } catch (error) {
      console.error('🚨 组件初始化失败:', error);
      // 确保Loading状态被清除
      tabConfigManager.setTabLoading(activeTab.value, false);
    }
  });

  // 组件卸载
  onUnmounted(() => {
    multiModuleI18nManager.clearModuleResources('clue-vue');
  });
</script>

<style lang="less" scoped>
  .statistics-dashboard {
    min-height: 100vh;
    background: #f5f5f5;

    .chart-item {
      height: 100%;
    }

    .floating-actions {
      position: fixed;
      bottom: 24px;
      right: 24px;
      z-index: 100;
    }

    .settings-content {
      .ant-form-item {
        margin-bottom: 24px;
      }
    }
  }
</style>
