<template>
  <!-- :class="{
      'opacity-80 transform rotate-1 cursor-grabbing': isDragging,
      'fixed inset-0 z-1000 rounded-none': isFullscreen,
    }" -->
  <LazyChartContainer
    :chart-id="config?.id || props.chartId"
    :chart-title="config?.title || '图表'"
    :lazy-options="lazyLoadOptions"
    :min-height="chartHeight"
    :style="gridColumnStyle"
    class="bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-300 hover:shadow-md"
  >
    <template #default>
      <!-- 自定义标题区（仅对可切换图表显示，且不在下探状态时显示） -->
      <div class="flex justify-center py-1 border-b border-gray-100">
        <!-- 非下探状态时显示切换标题，点击切换标题 -->
        <template v-if="!isInDrillDownState && config?.customProps?.switchable">
          <div class="cursor-pointer px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-50" @click="handleTitleSwitch">
            <div class="flex items-center gap-2">
              <span class="font-medium text-blue-600">{{ config?.title }}</span>
              <span class="text-gray-400">/</span>
              <span class="font-medium text-gray-500 hover:text-blue-500 transition-colors">{{ config?.customProps?.alternativeTitle }}</span>
              <Icon icon="ant-design:swap-outlined" class="ml-2 text-sm text-blue-500" />
            </div>
          </div>
        </template>
        <!-- 下探状态或者非可切换图表时显示当前标题，不显示切换标题 -->
        <template v-else>
          <div class="cursor-pointer px-4 py-2 rounded-md transition-all duration-200 hover:bg-blue-50">
            <div class="flex items-center gap-2">
              <span class="font-medium text-blue-600">{{ config?.title }}</span>
            </div>
          </div>
        </template>
      </div>

      <!-- 图表内容区 -->
      <div class="relative" :style="{ height: chartHeight }">
        <!-- Loading状态 -->
        <div v-if="isChartLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div class="flex flex-col items-center gap-3">
            <a-spin size="large" />
            <div class="text-gray-600 text-sm">{{ t('loadingText') }}</div>
          </div>
        </div>

        <!-- 无数据状态 -->
        <div v-else-if="isEmptyChartData" class="flex items-center justify-center h-full">
          <a-empty :description="t('emptyData')">
            <template #image>
              <Icon icon="ant-design:bar-chart-outlined" style="font-size: 48px; color: #d9d9d9" />
            </template>
            <a-button type="primary" size="small" @click="handleRefresh"> {{ t('redo') }} </a-button>
          </a-empty>
        </div>

        <!-- 图表内容 -->
        <div v-else class="h-full" :class="{ 'opacity-50': isChartLoading }">
          <EChartsComponent
            v-if="config"
            ref="chartRef"
            :config="config"
            :data="data"
            :height="chartHeight"
            :width="chartWidth"
            :draggable="draggable"
            :is-dragging="isDragging"
          />
        </div>
      </div>
    </template>
  </LazyChartContainer>
</template>

<script lang="ts">
  export default {
    name: 'ChartGroup',
  };
</script>
<script setup lang="ts">
  import { ref, computed } from 'vue';
  import { Icon } from '/@/components/Icon';
  import EChartsComponent from './EChartsComponent.vue';
  import LazyChartContainer from './LazyChartContainer.vue';
  import { useChartActionsOptional, useChartConfigOptional, useChartDataOptional, useChartStateOptional } from '../hooks/useChartActions';
  import { normalizeSizeValue } from '../utils';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { generateGridColumnStyle } from '../utils/layoutUtils';

  const { t } = useI18n('common');

  // Props定义 - 简化为只接收chartId，配置和数据从上下文系统获取
  interface Props {
    /** 图表ID */
    chartId: string;
    /** 是否可拖拽 */
    draggable?: boolean;
    /** 图表高度 */
    height?: string;
    /** 图表宽度 */
    width?: string;
    /** 外部loading状态 */
    loading?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    draggable: false,
    height: '450px',
    width: '100%',
    loading: false,
  });

  // 懒加载配置 - 暂时注释，用于问题排查
  const lazyLoadOptions = computed(() => ({
    rootMargin: '100px',
    threshold: 0.1,
    once: true,
    delay: Math.random() * 300 + 100, // 随机延迟，避免同时加载
  }));

  // 使用图表上下文系统
  const chartActions = useChartActionsOptional();
  const chartConfig = useChartConfigOptional();
  const chartData = useChartDataOptional();
  const chartState = useChartStateOptional();

  // 从上下文系统获取配置和数据
  const config = computed(() => {
    return chartConfig.getChartConfig(props.chartId);
  });

  const data = computed(() => {
    const cfg = config.value;
    if (!cfg) {
      return [];
    }

    const chartDataResult = chartData.getChartData(cfg.dataSource);
    console.log(
      `ChartGroup[${props.chartId}]: 数据获取 - 数据源: ${cfg.dataSource}, 数据长度: ${chartDataResult?.length || 0}, 数据: `,
      chartDataResult
    );

    return chartDataResult || [];
  });

  // 本地状态
  const chartRef = ref();
  const refreshing = ref(false);
  const isDragging = ref(false);
  const isFullscreen = ref(false);

  // 计算属性：综合loading状态 (繁琐，后续需要优化)
  const isChartLoading = computed(() => {
    const cfg = config.value;
    if (!cfg) return props.loading || refreshing.value;

    // 🔥 综合多个loading状态源
    const configLoading = cfg.customProps?.loading || false; // 配置中的loading状态
    const contextLoading = chartState.chartLoadingStates[props.chartId] || false; // 上下文中的loading状态
    const propsLoading = props.loading || false; // props传入的loading状态
    const localLoading = refreshing.value; // 本地刷新loading状态

    const finalLoading = configLoading || contextLoading || propsLoading || localLoading;

    // 调试日志
    if (finalLoading) {
      console.log(`ChartGroup[${props.chartId}] Loading状态:`, {
        configLoading,
        contextLoading,
        propsLoading,
        localLoading,
        finalLoading,
      });
    }

    return finalLoading;
  });

  // 计算属性：简化的空数据判断
  const isEmptyChartData = computed(() => {
    const cfg = config.value;

    // 如果正在加载，不显示空状态
    if (isChartLoading.value) {
      return false;
    }

    // 如果没有配置，显示空状态
    if (!cfg) {
      return true;
    }

    // 🔥 简化逻辑：直接检查配置中的isEmpty标记
    return cfg.customProps?.isEmpty === true;
  });

  // 计算属性
  const chartHeight = computed(() => {
    if (isFullscreen.value) return '80vh';

    // 优先使用配置中的尺寸，如果没有则使用props
    const cfg = config.value;
    if (cfg?.size?.height !== undefined) {
      const normalizedHeight = normalizeSizeValue(cfg.size.height);
      if (normalizedHeight) {
        return normalizedHeight;
      }
    }

    return props.height;
  });

  const chartWidth = computed(() => {
    if (isFullscreen.value) return '100%';

    // 优先使用配置中的尺寸，如果没有则使用props
    const cfg = config.value;
    if (cfg?.size?.width !== undefined) {
      const normalizedWidth = normalizeSizeValue(cfg.size.width);
      if (normalizedWidth) {
        return normalizedWidth;
      }
    }

    return props.width;
  });

  // 判断是否在下探状态 - 使用上下文系统
  const isInDrillDownState = computed(() => {
    const drillDownState = chartConfig.getChartDrillDownState(props.chartId);
    return drillDownState?.isInDrillDown || false;
  });

  // 计算网格布局样式
  const gridColumnStyle = computed(() => {
    const cfg = config.value;
    return generateGridColumnStyle(cfg?.layout);
  });

  /**
   * 处理标题切换
   */
  const handleTitleSwitch = () => {
    const cfg = config.value;
    if (!cfg?.customProps?.switchable || isChartLoading.value) return;
    const newDataSource = cfg.customProps.alternativeDataSource;
    if (newDataSource) {
      chartActions.switchChartDataSource(cfg.id, newDataSource);
    }
  };

  /**
   * 处理刷新 - 专注于数据刷新而不是重新加载组件
   */
  const handleRefresh = async () => {
    if (isChartLoading.value) return; // 防止重复刷新

    refreshing.value = true;

    try {
      // 直接使用上下文系统刷新图表
      const cfg = config.value;
      if (cfg) chartActions.refreshChart(cfg.id);

      // 数据刷新现在完全由上下文系统处理，组件会自动响应数据变化
    } catch (error) {
      console.error('刷新图表数据失败:', error);
    } finally {
      refreshing.value = false;
    }
  };
</script>
