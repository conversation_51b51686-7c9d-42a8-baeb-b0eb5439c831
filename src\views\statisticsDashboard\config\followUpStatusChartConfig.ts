import { useI18n } from '/@/hooks/web/useI18n';
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';
import type { ChannelConfig, ChartConfig, ChartDataItem, MonthlyChannelData } from '../types/statisticDashboard';
import { createTooltipFormatter, generateChartDataItem } from '../utils';
import { getDynamicFollowUpStatusChannelConfig } from '../utils/dataTransform';

const { t } = useI18n('common');

/**
 * 有效线索跟进状态枚举值
 */
export enum ClueValidStatusEnum {
  /** 待下发 */
  CLUE_STATUS_ONE = 30051001,
  /** 已下发待分配 */
  CLUE_STATUS_TWO = 30051002,
  /** 未跟进 */
  CLUE_STATUS_THREE = 30051003,
  /** 跟进中 */
  CLUE_STATUS_FOUR = 30051004,
  /** 已预约到店 */
  CLUE_STATUS_FIVE = 30051005,
  /** 到店跟进中 */
  CLUE_STATUS_SIX = 30051006,
  /** 已到店试驾 */
  CLUE_STATUS_SEVEN = 30051007,
  /** 已下定 */
  CLUE_STATUS_EIGHT = 30051008,
  /** 已成交 */
  CLUE_STATUS_NINE = 30051009,
  /** 已战败 */
  CLUE_STATUS_TEN = 30051010,
  /** 跟进无效 */
  CLUE_STATUS_ELEVEN = 30051011,
  /** 回收战败 */
  CLUE_STATUS_TWELVE = 30051012,
}

// 线索跟进状态颜色映射
const STATUS_COLOR_MAP = {
  [ClueValidStatusEnum.CLUE_STATUS_ONE]: '#1890ff',
  [ClueValidStatusEnum.CLUE_STATUS_TWO]: '#52c41a',
  [ClueValidStatusEnum.CLUE_STATUS_THREE]: '#faad14',
  [ClueValidStatusEnum.CLUE_STATUS_FOUR]: '#f5222d',
  [ClueValidStatusEnum.CLUE_STATUS_FIVE]: '#722ed1',
  [ClueValidStatusEnum.CLUE_STATUS_SIX]: '#13c2c2',
  [ClueValidStatusEnum.CLUE_STATUS_SEVEN]: '#eb2f96',
  [ClueValidStatusEnum.CLUE_STATUS_EIGHT]: '#fa8c16',
  [ClueValidStatusEnum.CLUE_STATUS_NINE]: '#a0d911',
  [ClueValidStatusEnum.CLUE_STATUS_TEN]: '#2f54eb',
  [ClueValidStatusEnum.CLUE_STATUS_ELEVEN]: '#fa541c',
  [ClueValidStatusEnum.CLUE_STATUS_TWELVE]: '#597ef7',
} as const;

// 线索跟进状态名称映射
export const getFollowUpStatusNameMap = () => {
  const useI18nClue = useI18nWithModule('clue-vue');
  return {
    [ClueValidStatusEnum.CLUE_STATUS_ONE]: t('pendingDistribution'),
    [ClueValidStatusEnum.CLUE_STATUS_TWO]: t('pendingAllocation'),
    [ClueValidStatusEnum.CLUE_STATUS_THREE]: t('notFollowedUp'),
    [ClueValidStatusEnum.CLUE_STATUS_FOUR]: useI18nClue.t('clueCenter.info.followUp'),
    [ClueValidStatusEnum.CLUE_STATUS_FIVE]: useI18nClue.t('clueCenter.info.reservationIsAvailable'),
    [ClueValidStatusEnum.CLUE_STATUS_SIX]: t('followUpInStore'),
    [ClueValidStatusEnum.CLUE_STATUS_SEVEN]: t('testedAtTheStore'),
    [ClueValidStatusEnum.CLUE_STATUS_EIGHT]: useI18nClue.t('clueCenter.info.decided'),
    [ClueValidStatusEnum.CLUE_STATUS_NINE]: useI18nClue.t('clueCenter.info.filled'),
    [ClueValidStatusEnum.CLUE_STATUS_TEN]: useI18nClue.t('clueCenter.info.defeated'),
    [ClueValidStatusEnum.CLUE_STATUS_ELEVEN]: t('invalidFollowUp'),
    [ClueValidStatusEnum.CLUE_STATUS_TWELVE]: t('recycleDefeated'),
  };
};

export const getFollowUpStatusColorMap = () => {
  return STATUS_COLOR_MAP;
};

/**
 * 线索跟进状态tooltip格式化器
 */
const formatFollowUpStatusTooltip = createTooltipFormatter({
  showPercentage: false,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: '' },
  extraInfoProvider: (_axisValue: string, params: any[]) => {
    if (!params || params.length === 0) return '';

    let customData = null;

    // 遍历所有参数，查找包含 customData 的数据项
    for (const param of params) {
      if (param.data && param.data.customData) {
        customData = param.data.customData;
        break;
      }
    }

    if (!customData) return '';

    const useI18nClue = useI18nWithModule('clue-vue');
    const { followUpCount, totalCount, followUpPercent } = customData;

    const followingUpPercentage = totalCount > 0 ? ((followUpCount / totalCount) * 100).toFixed(1) : '0.0';

    return [
      `${t('total')}：${totalCount}`,
      `${useI18nClue.t('clueCenter.info.followUp')}：${followUpCount} (${followingUpPercentage}%)`,
      `${useI18nClue.t('clueCenter.info.followUpRate')}：${followUpPercent}%`,
    ].join('<br/>');
  },
});

/**
 * 线索跟进状态图表配置管理器
 * 专注于线索跟进状态图表的静态配置生成，不维护运行时数据
 * 参考UTM图表配置管理器的设计原则
 */
export class FollowUpStatusChartConfigManager {
  private static instance: FollowUpStatusChartConfigManager;
  private loadingStrategy = 'follow-up-status-chart';

  // 常量配置，避免重复定义
  private readonly TREND_LINE_COLOR = '#8B0000';
  private readonly STACK_NAME = 'followUpStatus';

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): FollowUpStatusChartConfigManager {
    if (!FollowUpStatusChartConfigManager.instance) {
      FollowUpStatusChartConfigManager.instance = new FollowUpStatusChartConfigManager();
    }
    return FollowUpStatusChartConfigManager.instance;
  }

  /**
   * 创建堆叠系列的基础配置
   */
  private createStackSeries(channelConfig: ChannelConfig[]): any[] {
    return channelConfig.map((channelItem) => ({
      name: channelItem.name,
      type: 'bar',
      stack: this.STACK_NAME,
      data: [],
      itemStyle: { color: channelItem.color },
      label: { show: false },
    }));
  }

  /**
   * 创建趋势线系列的基础配置
   */
  private createTrendLineSeries(name: string, yAxisIndex: number = 0): any {
    return {
      name,
      type: 'line',
      yAxisIndex,
      data: [],
      itemStyle: { color: this.TREND_LINE_COLOR },
      lineStyle: { color: this.TREND_LINE_COLOR, width: 2 },
      symbol: 'circle',
      symbolSize: 6,
    };
  }

  /**
   * 创建双Y轴配置
   */
  private createDualYAxisConfig(): any[] {
    const useI18nClue = useI18nWithModule('clue-vue');
    return [
      { type: 'value', name: t('numberOfClues'), position: 'left' },
      {
        type: 'value',
        name: useI18nClue.t('clueCenter.info.followUpRate'),
        position: 'right',
        min: 0,
        max: 100,
        axisLabel: { formatter: '{value}%' },
      },
    ];
  }

  /**
   * 为数据项添加 customData
   */
  private addCustomDataToItem(dataItem: any, monthData: ChartDataItem): any {
    if (monthData.customData) dataItem.customData = monthData.customData;
    return dataItem;
  }

  /**
   * 生成基础线索跟进状态图表配置（不包含数据）
   */
  public generateBaseConfig(validStatusList?: number[]): ChartConfig {
    const channelConfig = this.getChannelConfig(validStatusList);
    const series = [...this.createStackSeries(channelConfig), this.createTrendLineSeries(t('totalTrend'), 0)];

    return {
      id: 'followUpStatusChart',
      type: 'bar',
      title: t('followUpStatusOfClues'),
      dataSource: 'followUpStatus',
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
        validStatusList,
      },
      options: {
        title: { show: false },
        color: [...channelConfig.map((config) => config.color), this.TREND_LINE_COLOR],
        grid: { left: '4%', right: '4%', bottom: '15%', containLabel: true },
        legend: {
          data: [...channelConfig.map((config) => config.name), t('totalTrend')],
          bottom: '5%',
          left: 'center',
          type: 'scroll',
        },
        xAxis: { type: 'category', data: [], axisLabel: { interval: 0, rotate: 30 } },
        yAxis: { type: 'value', name: t('numberOfClues') },
        series: series as any,
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: formatFollowUpStatusTooltip,
        },
      },
      size: { height: 500 },
    };
  }

  /**
   * 更新图表配置的数据部分
   */
  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[], validStatusList?: number[]): Partial<ChartConfig> {
    const channelConfig = this.getChannelConfig(validStatusList);
    const useI18nClue = useI18nWithModule('clue-vue');
    const months = chartData.map((item) => item.name);

    const stackSeries = this.createStackSeries(channelConfig).map((seriesItem, index) => ({
      ...seriesItem,
      data: chartData.map((monthData) => {
        const channelItem = channelConfig[index];
        const value = monthData.channels[channelItem.key] || 0;
        const dataItem = generateChartDataItem(monthData.name, value, '0');
        return this.addCustomDataToItem(dataItem, monthData);
      }),
    }));

    const followUpRateData = chartData.map((monthData, index) => {
      const followUpPercent = monthData.customData?.followUpPercent || 0;
      const dataItem: any = {
        name: monthData.name,
        value: followUpPercent,
        label: {
          show: index === 0 || index === chartData.length - 1,
          position: 'top',
          formatter: '{c}%',
          fontSize: 12,
          color: '#8B0000',
        },
      };
      return this.addCustomDataToItem(dataItem, monthData);
    });

    const followUpRateSeries = {
      ...this.createTrendLineSeries(useI18nClue.t('clueCenter.info.followUpRate'), 1),
      data: followUpRateData,
    };

    const allSeries = [...stackSeries, followUpRateSeries];

    return {
      options: {
        ...baseConfig.options,
        xAxis: { ...baseConfig.options?.xAxis, type: 'category', data: months } as any,
        yAxis: this.createDualYAxisConfig(),
        series: allSeries as any,
        legend: { ...baseConfig.options?.legend, data: [...channelConfig.map((config) => config.name)] },
        tooltip: { ...baseConfig.options?.tooltip },
      },
      customProps: {
        ...baseConfig.customProps,
        loading: false,
        isEmpty: chartData.length === 0,
        lastUpdated: new Date().toISOString(),
      },
    };
  }

  /**
   * 获取线索跟进状态的渠道配置
   */
  private getChannelConfig(validStatusList?: number[]): ChannelConfig[] {
    return validStatusList?.length ? getDynamicFollowUpStatusChannelConfig(validStatusList) : [];
  }
}

export const followUpStatusChartConfigManager = FollowUpStatusChartConfigManager.getInstance();
export const generateBaseFollowUpStatusChartConfig = (validStatusList?: number[]): ChartConfig =>
  followUpStatusChartConfigManager.generateBaseConfig(validStatusList);
export const updateFollowUpStatusChartData = (baseConfig: ChartConfig, chartData: MonthlyChannelData[]): Partial<ChartConfig> =>
  followUpStatusChartConfigManager.updateChartData(baseConfig, chartData);
export const getFollowUpStatusChartConfigManager = () => followUpStatusChartConfigManager;
export const followUpStatusChartConfig: ChartConfig = generateBaseFollowUpStatusChartConfig();
export { STATUS_COLOR_MAP };
export default followUpStatusChartConfigManager;
