# 多模块国际化系统使用指南

## 概述

本项目实现了一个多模块国际化资源管理系统，解决了多个模块的翻译资源可能发生冲突的问题。该系统在保持现有 `common` 命名空间逻辑完全兼容的基础上，支持多个模块的独立资源管理。

## 架构设计

### 1. 命名空间映射

不同的 `resourceType` 被映射到不同的命名空间，避免键值冲突：

```typescript
const MODULE_NAMESPACE_MAP = {
  'UNIFIED_FRONT_END': 'common',        // 保持现有逻辑不变
  'clue-vue': 'clue',                   // 线索模块使用 clue 命名空间
  'work-order-vue': 'workOrder'         // 工单模块使用 workOrder 命名空间
};
```

### 2. 数据结构

多语言资源在 localStorage 中的存储结构：

```json
{
  "common": {
    "key1": {
      "zh-CN": "中文值",
      "en-US": "英文值"
    }
  },
  "clue": {
    "clueStatus": {
      "zh-CN": "线索状态",
      "en-US": "Clue Status"
    }
  },
  "workOrder": {
    "workOrderType": {
      "zh-CN": "工单类型",
      "en-US": "Work Order Type"
    }
  }
}
```

## 核心组件

### 1. MultiModuleI18nManager

多模块国际化资源管理器，提供以下功能：

- 模块资源的独立加载和缓存
- 防重复加载机制
- 资源合并和持久化
- 模块状态管理

### 2. useI18nWithModule

带自动加载功能的模块化Hook：

```typescript
const {
  t,           // 翻译函数
  isLoaded,    // 是否已加载
  loading,     // 加载状态
  error,       // 错误信息
  loadModule,  // 手动加载函数
  namespace    // 命名空间
} = useI18nWithModule('clue-vue');
```

## 使用方式

### 1. 基础使用（保持兼容）

现有的国际化使用方式完全不受影响：

```typescript
// 在任何组件中
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();
const text = t('common.someKey'); // 继续使用现有方式
```

### 2. 新模块资源使用（推荐方式）

**对于所有新的模块多语言需求，统一使用 `useI18nWithModule`：**

```typescript
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

// 使用线索模块资源
const { t, isLoaded, loading, error } = useI18nWithModule('clue-vue');

// 使用工单模块资源
const { t: tWorkOrder, isLoaded: workOrderLoaded } = useI18nWithModule('work-order-vue');

// 等待资源加载完成后使用
if (isLoaded.value) {
  const clueStatusText = t('clueStatus');
}

if (workOrderLoaded.value) {
  const workOrderTypeText = tWorkOrder('workOrderType');
}
```

#### 核心优势

- **自动加载管理**：Hook 会自动处理模块资源的加载，避免重复请求
- **状态监控**：提供 `loading`、`error`、`isLoaded` 状态，便于 UI 反馈
- **性能优化**：内置缓存机制，只在模块未加载时才发起请求
- **类型安全**：完整的 TypeScript 支持
- **简单易用**：一行代码即可获得完整的模块多语言能力

#### 使用模式

```typescript
// 基础使用
const { t, isLoaded, loading } = useI18nWithModule('clue-vue');

// 监听加载状态
watch(isLoaded, (loaded) => {
  if (loaded) {
    console.log('模块资源加载完成，可以使用翻译功能');
  }
});

// 错误处理
watch(error, (err) => {
  if (err) {
    console.error('模块资源加载失败:', err);
  }
});

// 条件渲染
const displayText = computed(() => {
  return isLoaded.value ? t('someKey') : '加载中...';
});
```

## 在统计看板中的实际应用

### 1. 页面级集成

在 `UserOperationStatisticsDashboard.vue` 中：

```typescript
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

// 在 setup 中
const { t: tClue, isLoaded: clueLoaded, loading: clueLoading } = useI18nWithModule('clue-vue');
const { t: tWorkOrder, isLoaded: workOrderLoaded } = useI18nWithModule('work-order-vue');

// 监听加载状态
watch(clueLoaded, (loaded) => {
  if (loaded) {
    console.log('线索模块多语言资源加载完成');
    // 可以触发相关组件的数据刷新
  }
});
```

### 2. 组件级使用

在统计图表组件中：

```vue
<template>
  <div class="clue-chart">
    <h3 v-if="clueLoaded">{{ tClue('totalClues') }}</h3>
    <div v-if="clueLoading">{{ tClue('loading') }}</div>
    <!-- 图表内容 -->
  </div>
</template>

<script setup>
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

const { t: tClue, isLoaded: clueLoaded, loading: clueLoading } = useI18nWithModule('clue-vue');
</script>
```

### 3. 多模块混合使用

```vue
<template>
  <div class="dashboard">
    <!-- 线索相关 -->
    <div v-if="clueLoaded">
      <h2>{{ tClue('clueCenter.title') }}</h2>
      <p>{{ tClue('clueCenter.description') }}</p>
    </div>
    
    <!-- 工单相关 -->
    <div v-if="workOrderLoaded">
      <h2>{{ tWorkOrder('workOrder.title') }}</h2>
      <p>{{ tWorkOrder('workOrder.description') }}</p>
    </div>
  </div>
</template>

<script setup>
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

// 同时使用多个模块
const { t: tClue, isLoaded: clueLoaded } = useI18nWithModule('clue-vue');
const { t: tWorkOrder, isLoaded: workOrderLoaded } = useI18nWithModule('work-order-vue');
</script>
```

## 最佳实践

### 1. 使用 useI18nWithModule 的最佳实践

#### 命名约定

```typescript
// 推荐：使用描述性的变量名
const { t: tClue, isLoaded: clueLoaded, loading: clueLoading } = useI18nWithModule('clue-vue');
const { t: tWorkOrder, isLoaded: workOrderLoaded } = useI18nWithModule('work-order-vue');

// 避免：使用通用名称可能导致冲突
// const { t, isLoaded } = useI18nWithModule('clue-vue'); // 不推荐
```

#### 加载状态处理

```typescript
const { t, isLoaded, loading, error } = useI18nWithModule('clue-vue');

// 1. 条件渲染
const displayText = computed(() => {
  if (loading.value) return '加载中...';
  if (error.value) return '加载失败';
  if (!isLoaded.value) return '';
  return t('someKey');
});

// 2. 监听状态变化
watch(isLoaded, (loaded) => {
  if (loaded) {
    // 资源加载完成后的逻辑
    console.log('模块资源已就绪');
  }
});
```

### 2. 错误处理

```typescript
const { t, error, loading } = useI18nWithModule('clue-vue');

// 监听错误
watch(error, (err) => {
  if (err) {
    console.error('加载线索模块多语言资源失败:', err);
    // 显示错误提示或使用默认文本
  }
});
```

### 3. 性能优化

- **自动缓存**：`useI18nWithModule` 内置防重复加载机制
- **按需加载**：只在组件需要时才加载对应模块资源
- **状态共享**：同一模块的多个 Hook 实例共享加载状态

### 4. 调试和监控

```typescript
// 查看已加载的模块
const loadedModules = multiModuleI18nManager.getLoadedModules();
console.log('已加载的多语言模块:', loadedModules);

// 检查特定模块是否已加载
const isClueLoaded = multiModuleI18nManager.isModuleLoaded('clue-vue');
console.log('线索模块是否已加载:', isClueLoaded);
```

## 迁移指南

### 现有代码无需修改

现有使用 `useI18n` 的代码完全不需要修改，系统会自动兼容：

```typescript
// 这些代码继续正常工作
const { t } = useI18n();
const { t } = useI18n('common');
const text = t('common.someKey');
```

### 新功能采用指南

**对于所有新的模块多语言需求，统一使用 `useI18nWithModule`：**

```typescript
// 新页面中使用 useI18nWithModule
const { t: tClue, isLoaded: clueLoaded } = useI18nWithModule('clue-vue');
const { t: tWorkOrder, isLoaded: workOrderLoaded } = useI18nWithModule('work-order-vue');

// 替代之前可能考虑的其他方式
// ❌ 不再推荐创建专用 Hook
// ❌ 不再推荐手动管理加载
// ✅ 统一使用 useI18nWithModule
```

### 迁移步骤

1. **识别需要多语言的模块**：确定哪些页面需要加载特定模块的翻译资源
2. **引入 useI18nWithModule**：在需要的组件中导入并使用
3. **处理加载状态**：根据 `isLoaded` 状态决定何时显示翻译内容
4. **错误处理**：监听 `error` 状态，提供友好的错误提示

## 故障排除

### 1. 资源加载失败

```typescript
// 检查网络连接和API接口
const { error } = useI18nWithModule('clue-vue');
if (error.value) {
  console.error('资源加载失败:', error.value);
}
```

### 2. 翻译不生效

```typescript
// 检查模块是否已加载
const { isLoaded } = useI18nWithModule('clue-vue');
if (!isLoaded.value) {
  console.warn('模块资源尚未加载完成');
}

// 检查翻译键是否存在
// 可以通过尝试翻译来检查键是否存在
const translatedText = t('someKey');
if (translatedText === 'someKey') {
  console.warn('翻译键不存在或翻译失败:', 'someKey');
}
```

### 3. 命名空间冲突

如果发现命名空间冲突，可以修改 `MODULE_NAMESPACE_MAP` 配置：

```typescript
// 在 multiModuleI18nManager.ts 中修改映射
const MODULE_NAMESPACE_MAP = {
  'UNIFIED_FRONT_END': 'common',
  'clue-vue': 'clueModule',  // 修改为更具体的命名空间
  'work-order-vue': 'workOrderModule'
};
```

## 总结

这个多模块国际化系统提供了以下优势：

1. **向后兼容**：现有代码无需修改
2. **模块隔离**：不同模块的翻译资源互不冲突
3. **按需加载**：只加载需要的模块资源
4. **易于扩展**：可以轻松添加新的模块支持
5. **性能优化**：防重复加载和智能缓存
6. **开发友好**：提供丰富的调试和监控功能

通过这个系统，可以在统计看板页面安全地加载 `clue-vue` 模块的多语言资源，而不会与现有的 `common` 命名空间发生冲突。