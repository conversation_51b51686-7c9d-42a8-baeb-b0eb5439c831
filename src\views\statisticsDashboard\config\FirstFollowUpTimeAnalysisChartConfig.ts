import type { ChartConfig, ChartDataItem } from '../types/statisticDashboard';
import { createTooltipFormatter } from '../utils';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

/**
 * 线索跟进时间区间配置
 */
export const followUpTimeRangeConfig = [
  { key: 'within_4h', name: `0-4${t('time_hours')}` },
  { key: 'within_8h', name: `4-8${t('time_hours')}` },
  { key: 'within_12h', name: `8-12${t('time_hours')}` },
  { key: 'within_16h', name: `12-16${t('time_hours')}` },
  { key: 'within_20h', name: `16-20${t('time_hours')}` },
  { key: 'within_24h', name: `20-24${t('time_hours')}` },
  { key: 'over_24h', name: `>24${t('time_hours')}` },
];

/**
 * 线索跟进时间tooltip格式化函数
 */
const formatter = createTooltipFormatter({
  showPercentage: true,
  showTotal: false,
  excludeSeriesTypes: [],
  specialSeries: undefined,
  extraInfoProvider: (_axisValue: string, params: any[]) => {
    if (!params || params.length === 0) return '';
    const trendRate = params[1].value || 0;
    return `${t('trendRate')}: ${trendRate}%`;
  },
});

/**
 * 线索首次跟进时长图表配置管理器
 */
export class FirstFollowUpTimeAnalysisChartConfigManager {
  private static instance: FirstFollowUpTimeAnalysisChartConfigManager;
  private loadingStrategy = 'clue-follow-up-time';

  private constructor() {
    // 私有构造函数，确保单例模式
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): FirstFollowUpTimeAnalysisChartConfigManager {
    if (!FirstFollowUpTimeAnalysisChartConfigManager.instance) {
      FirstFollowUpTimeAnalysisChartConfigManager.instance = new FirstFollowUpTimeAnalysisChartConfigManager();
    }
    return FirstFollowUpTimeAnalysisChartConfigManager.instance;
  }

  /**
   * 生成基础图表配置
   */
  public generateBaseConfig(): ChartConfig {
    return {
      id: 'clueFollowUpTime',
      type: 'bar',
      title: t('analysisOfInitialFollowUpDuration'),
      dataSource: 'clueFollowUpTime',
      loadingStrategy: this.loadingStrategy,
      customProps: {
        needsAsyncData: true,
      },
      options: {
        title: {
          show: false,
        },
        // color: followUpTimeRangeConfig.map((config) => config.color),
        color: '#5470c6',
        grid: {
          left: '4%',
          right: '4%',
          bottom: '15%',
          containLabel: true,
        },
        legend: {
          bottom: '5%',
          left: 'center',
          type: 'scroll',
          data: [t('numberOfClues'), t('trendRate')],
        },
        xAxis: {
          type: 'category',
          data: followUpTimeRangeConfig.map((config) => config.name),
          axisLabel: {
            interval: 0,
            rotate: 0,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: t('numberOfClues'),
            position: 'left',
            axisLabel: {
              formatter: '{value}',
            },
            nameTextStyle: {
              fontSize: 12,
              color: '#666',
            },
          },
          {
            type: 'value',
            name: `${t('trendRate')}(%)`,
            position: 'right',
            min: 0,
            max: 30, // 根据图片数据调整最大值
            axisLabel: {
              formatter: '{value}%',
            },
            nameTextStyle: {
              fontSize: 12,
              color: '#666',
            },
          },
        ],
        series: [],
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: formatter,
        },
      },
      size: { height: 500 },
    };
  }

  /**
   * 更新图表数据
   */
  public updateChartData(baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> {
    if (!chartData || chartData.length === 0) {
      return {
        ...baseConfig,
        options: {
          ...baseConfig.options,
          series: [],
        },
      };
    }

    // 生成柱状图系列配置
    const barSeries = {
      name: t('numberOfClues'),
      type: 'bar',
      data: chartData.map((item) => {
        const percentage = item.percentage || 0;
        return {
          name: item.name,
          value: item.value,
          itemStyle: {
            color: '#5470c6',
          },
          label: {
            show: true,
            position: 'top',
            formatter: () => {
              return `${item.value}\n(${percentage.toFixed(1)}%)`;
            },
            fontSize: 12,
          },
        };
      }),
      barWidth: '60%',
    };

    // 生成占比趋势折线系列配置
    const trendLineSeries = {
      name: t('trendRate'),
      type: 'line',
      yAxisIndex: 1, // 使用右侧Y轴
      data: chartData.map((item) => ({
        name: item.name,
        value: item.percentage,
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%',
          fontSize: 12,
          color: '#ff4d4f', // 红色标签
        },
      })),
      itemStyle: {
        color: '#ff4d4f', // 红色数据点
      },
      lineStyle: {
        color: '#ff4d4f', // 红色线条
        width: 2,
      },
      symbol: 'circle',
    };

    return {
      ...baseConfig,
      options: {
        ...baseConfig.options,
        xAxis: {
          type: 'category',
          data: chartData.map((item) => item.name),
          axisLabel: {
            interval: 0,
            rotate: 0,
          },
        },
        series: [barSeries, trendLineSeries] as any,
      },
    };
  }
}

// 导出单例实例和便捷函数
export const firstFollowUpTimeAnalysisChartConfigManager = FirstFollowUpTimeAnalysisChartConfigManager.getInstance();

export const generateBaseFirstFollowUpTimeAnalysisChartConfig = (): ChartConfig => firstFollowUpTimeAnalysisChartConfigManager.generateBaseConfig();

export const updateFirstFollowUpTimeAnalysisChartData = (baseConfig: ChartConfig, chartData: ChartDataItem[]): Partial<ChartConfig> =>
  firstFollowUpTimeAnalysisChartConfigManager.updateChartData(baseConfig, chartData);

export const getFirstFollowUpTimeAnalysisChartConfigManager = () => firstFollowUpTimeAnalysisChartConfigManager;

export const firstFollowUpTimeAnalysisChartConfig: ChartConfig = generateBaseFirstFollowUpTimeAnalysisChartConfig();

export default firstFollowUpTimeAnalysisChartConfigManager;
