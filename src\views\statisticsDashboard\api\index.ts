import { defHttpStatistics } from '/@/utils/http/axios';
import { StatisticalDimensionTypeEnum } from '../enums';

/**
 * 看板统计API
 */

export interface commonQueryParams {
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 统计维度 */
  statisticalDimension: StatisticalDimensionTypeEnum;
  /** 城市编码 */
  cityCode?: string;
  /** 国家编码 */
  countryCode?: string;
  /** 经销商编码 */
  dealerCode?: string;
  /** 省份编码 */
  provinceCode?: string;
  /** 大区编码 */
  regionCenterCode?: string; //
  /** 区域编码 */
  regionCode?: string;
}

export interface QueryAllClueSourceParams extends commonQueryParams {}

/**
 * 查询全量线索来源
 */
export const queryAllClueSource = (data: QueryAllClueSourceParams) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllOneSource',
    data,
  });
};

/** 全量线索来源返回数据结构 & 有效线索来源返回数据结构 */
export interface AllClueSourceResponseType {
  clueOneSourceResponseList: [
    {
      clueOneSourceDtoList: [
        {
          oneSourceCount: number; // 一级来源数量
          oneSourceId: string; // 一级线索ID -对应 ClueSourceEnum
          oneSourceName: string; // 一级来源名称
          oneSourcePercent: number; // 一级来源占比
        },
      ];
      statisticsDate: string; // 一级来源统计日期
    },
  ];
  clueTwoSourceResponseList: [
    {
      clueTwoSourceDtoList: [
        {
          oneSourceName: string; // 一级来源名称
          twoSourceCount: number; // 二级来源数量
          twoSourceId: string; // 二级线索ID -对应 ClueSourceEnum
          twoSourceName: string; // 二级来源名称
          twoSourcePercent: number; // 二级来源占比
        },
      ];
      statisticsDate: string; // 二级来源统计日期
    },
  ];
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

export interface QueryAllClueSourceSecondParams extends commonQueryParams {
  /** 一级来源 ID */
  oneSourceId: string; // 对应 ClueSourceEnum
}

/**
 * 查询全量线索下探到二级来源
 */
export const queryAllClueSourceSecond = (data: Partial<QueryAllClueSourceSecondParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllTwoSource',
    data,
  });
};

/**
 * 查询有效线索一级来源
 */
export const queryValidClueSource = (data: QueryAllClueSourceParams) => {
  return defHttpStatistics.post({
    url: '/clue/valid/v1/clueValidOneSource',
    data,
  });
};

/**
 * 查询有效线索下探到二级来源
 */
export const queryValidClueSourceSecond = (data: Partial<QueryAllClueSourceSecondParams>) => {
  return defHttpStatistics.post({
    url: '/clue/valid/v1/clueValidTwoSource',
    data,
  });
};

interface OverViewCount {
  count: number | string; // 数量
  ringRatio: number; // 环比
  ringRatioTrend: boolean; // 环比趋势，true 上升 false 下降
  yearRatio: number; // 同比
  yearRatioTrend: boolean; // 同比趋势，true 上升 false 下降
  percent: number | string; // 百分比
}

export interface ClueOverViewResponse {
  // 全量线索总量
  clueAllCount: OverViewCount;
  // 线索成交量
  clueDealCount: OverViewCount;
  // 线索跟进率
  clueFollowPercent: OverViewCount;
  // 有效线索总量
  clueValidCount: OverViewCount;
  // 线索有效率
  clueValidPercent: OverViewCount;
  // 线索战胜率
  clueWinPercent: OverViewCount;
}

/**
 * 查询线索总览
 */
export const queryClueOverView = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueOverView',
    data,
  });
};

export interface ClueUtmInfoResponse {
  // 线索utm详情列表
  clueUtmDtoList: [
    {
      utmCount: number; // UTM 中类数量
      utmName: string; // UTM 中类名称
    },
  ];
  // 线索utm总量走势
  clueUtmTotal: number;
  // 线索utm统计日期
  statisticsDate: string;
}

export interface ClueUtmResponse {
  // utmMedium列表
  clueUtmMediumList: ClueUtmInfoResponse[];
  // utmSource列表
  clueUtmSourceList: ClueUtmInfoResponse[];
  // UTM渠道列表（动态生成的渠道名称）
  utmList: string[];
}

/**
 * 查询全量线索UTM来源
 */
export const queryAllClueUtmSource = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllUtmSource',
    data,
  });
};

/**
 * 查询全量线索UTMMedium来源
 */
export const queryAllClueUtmMediumSource = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllUtmMedium',
    data,
  });
};

/** 效线索有效性返回数据结构 */
export interface ClueValidStatisticsResponse {
  clueValidStatisticsInfoList: {
    clueAllTotal: number; // 线索总量
    clueValidPercent: number; // 线索有效率
    clueValidTotal: number; // 有效线索总量
    statisticsDate: string; // 统计日期
  }[];
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

/**
 * 查询线索有效性
 */
export const queryClueValidStatistics = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueValidStatistics',
    data,
  });
};

/**
 * 有效线索跟进状态响应数据结构
 */
export interface ClueValidStatusResponse {
  clueValidStatusInfoList: {
    clueUtmDtoList: {
      statusCount: number; // 该状态的数量
      validStatus: number; // 该状态的枚举值
    }[];
    clueValidStatusFollowCount: number; // 有效线索跟进状态数量
    clueValidStatusFollowPercent: number; // 有效线索跟进状态占比
    clueValidStatusTotal: number; // 有效线索状态总量
    statisticsDate: string; // 有效线索状态统计日期
  }[];
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
  validStatusList: number[]; // 有效线索状态枚举值列表 对应 ClueValidStatusEnum 的值
}

/**
 * 查询有效线索跟进状态
 */
export const queryClueValidStatus = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/valid/v1/clueValidStatus',
    data,
  });
};

export interface ClueValidFollowTimeDto {
  followTimeCount: number; // 有效线索首次跟进时长区间内的数量
  followTimePercent: number; //有效线索首次跟进时长区间内的百分比
}

// 有效线索首次跟进时长分析响应数据结构
export interface ClueValidFollowTimeResponse {
  eightHour: ClueValidFollowTimeDto; // 4-8小时
  followTimeTotal: number; // 状态进入过跟进中状态的线索总量
  fourHour: ClueValidFollowTimeDto; // 0-4小时
  overTwentyFourHour: ClueValidFollowTimeDto; // 大于24小时
  sixteenHour: ClueValidFollowTimeDto; // 12-16小时
  twelveHour: ClueValidFollowTimeDto; // 8-12 小时
  twentyFourHour: ClueValidFollowTimeDto; // 22-24 小时
  twentyHour: ClueValidFollowTimeDto; // 12-16 小时
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

/**
 * 查询有效线索首次跟进时长分析
 */
export const queryClueValidFirstFollowTime = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/valid/v1/clueValidFirstFollowTime',
    data,
  });
};

/**
 * 全量线索首次响应时长分析响应数据结构
 */
export interface ClueAllFirstRespOverTimeResponse {
  firstRespOverTimeInfoList: {
    normalCount: number; // 未超时的线索数量（72小时内）
    normalPercent: number; // 未超时百分比
    statisticsDate: string; // 统计日期
    timeoutCount: number; // 超时的线索数量（超过72小时）
    timeoutPercent: number; // 超时百分比
    totalCount: number; // 线索总量
  }[];
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

/**
 * 查询全量线索首次响应时长分析
 */
export const queryClueAllFirstRespOverTime = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/all/v1/clueAllFirstRespOverTime',
    data,
  });
};

export interface ClueOverViewPercentDto {
  percent: number; // 百分比
  ringRatio: number; // 环比
  yearRatio: number; // 同比
}

export interface ClueOverViewCountDto {
  count: number; // 数量
  ringRatio: number; // 环比
  yearRatio: number; // 同比
}

export interface ClueConversionDataResponse {
  clueConversionAverageCycles: ClueOverViewPercentDto; // 线索平均转化周期
  clueConversionTotalPercent: ClueOverViewPercentDto; // 线索总体转化率
  clueLosePercent: ClueOverViewPercentDto; // 线索战败率
  clueValidDealCount: ClueOverViewCountDto; // 已成交数
  clueValidTotalCount: ClueOverViewCountDto; // 总线索数
  statisticalDimension: StatisticalDimensionTypeEnum; // 统计维度
}

/**
 * 查询线索转化数据
 */
export const queryClueConversionData = (data: Partial<commonQueryParams>) => {
  return defHttpStatistics.post({
    url: '/clue/valid/v1/conversionData',
    data,
  });
};
