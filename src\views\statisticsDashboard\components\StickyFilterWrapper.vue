<template>
  <div class="sticky-filter-wrapper mb-4">
    <!-- 筛选器容器 -->
    <div ref="filterContainerRef" class="filter-container">
      <!-- 占位元素 - 仅在吸顶时显示，保持布局 -->
      <div v-show="isSticky" class="filter-placeholder" :style="{ height: `${filterHeight}px` }"></div>

      <!-- 单一筛选器组件 - 通过动态样式控制定位 -->
      <div
        ref="filterContentRef"
        class="filter-content"
        :class="{
          'is-sticky': isSticky,
          'sticky-enter': isAnimating,
        }"
        :style="dynamicStyles"
      >
        <!-- 单一筛选器组件实例 -->
        <slot name="filter" :isSticky="isSticky"></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'StickyFilterWrapper',
  };
</script>
<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch, nextTick, type CSSProperties } from 'vue';
  import { useStickyFilter } from '../hooks/useStickyFilter';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { debounce } from 'lodash-es';

  interface Props {
    /** 额外的顶部偏移量 */
    extraTopOffset?: number;
    /** 触发吸顶的额外阈值 */
    triggerThreshold?: number;
    /** 头部区域选择器 */
    headerSelector?: string;
    /** 导航栏选择器 */
    navSelector?: string;
    /** 自定义吸顶样式 */
    customStickyStyles?: Record<string, any>;
  }

  interface Emits {
    (e: 'sticky-change', isSticky: boolean): void;
    (e: 'dimensions-update', dimensions: { filterHeight: number; topOffset: number }): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    extraTopOffset: 0,
    triggerThreshold: 10,
    headerSelector: '.ant-layout-header',
    navSelector: '.ant-tabs-nav',
    customStickyStyles: () => ({}),
  });

  const emit = defineEmits<Emits>();

  // 动画控制状态
  const isAnimating = ref(false);

  // 使用吸顶功能Hook
  const {
    filterContainerRef,
    filterContentRef,
    isSticky,
    filterHeight,
    originalWidth,
    originalLeft,
    topOffset,
    scrollTop,
    stickyThreshold,
    initStickyFilter,
    destroyStickyFilter,
    updateDimensions,
    handleLayoutChange,
  } = useStickyFilter({
    extraTopOffset: props.extraTopOffset,
    triggerThreshold: props.triggerThreshold,
    headerSelector: props.headerSelector,
    navSelector: props.navSelector,
  });

  // 🎯 修复方案：监听侧边栏状态变化
  const { getCollapsed } = useMenuSetting();

  // 监听侧边栏折叠状态变化
  watch(
    getCollapsed,
    () => {
      // 🔧 修复：使用handleLayoutChange处理侧边栏变化
      // 这个函数会正确处理吸顶状态的临时取消和恢复，确保宽度正确更新
      handleLayoutChange();
    },
    { immediate: false }
  );

  // 🔧 修复：计算动态样式，支持响应式布局
  const dynamicStyles = computed((): CSSProperties => {
    if (!isSticky.value) {
      // 非吸顶状态：正常样式
      return {
        position: 'relative',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
      };
    }

    // 🔧 修复：响应式吸顶样式计算
    const isMobile = window.innerWidth <= 768;
    const isSmallMobile = window.innerWidth <= 480;

    let stickyStyles: CSSProperties;

    if (isMobile) {
      // 移动端：使用全宽布局，添加边距
      const horizontalMargin = isSmallMobile ? 4 : 8;
      stickyStyles = {
        position: 'fixed',
        top: `${topOffset.value + 10}px`,
        left: `${horizontalMargin}px`,
        right: `${horizontalMargin}px`,
        width: 'auto',
        zIndex: 999,
        backgroundColor: '#fff',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        borderRadius: isSmallMobile ? '12px' : '8px',
        backdropFilter: 'blur(8px)',
        border: '1px solid rgba(0, 0, 0, 0.06)',
      };
    } else {
      // 桌面端：使用原始位置和宽度
      stickyStyles = {
        position: 'fixed',
        top: `${topOffset.value + 10}px`,
        left: `${originalLeft.value}px`,
        width: `${originalWidth.value}px`,
        zIndex: 999,
        backgroundColor: '#fff',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        borderRadius: '8px',
        backdropFilter: 'blur(8px)',
        border: '1px solid rgba(0, 0, 0, 0.06)',
      };
    }

    // 合并自定义样式
    return { ...stickyStyles, ...props.customStickyStyles };
  });

  // 监听吸顶状态变化 - 使用防抖优化
  const debouncedStickyChange = debounce((isSticky: boolean) => {
    emit('sticky-change', isSticky);
  }, 100);

  // 监听吸顶状态变化，控制动画
  watch(
    isSticky,
    async (newIsSticky) => {
      if (newIsSticky) {
        // 进入吸顶状态，触发动画
        await nextTick();
        isAnimating.value = true;
        setTimeout(() => {
          isAnimating.value = false;
        }, 400);
      }

      debouncedStickyChange(newIsSticky);
    },
    { immediate: true }
  );

  // 监听尺寸变化
  watch(
    [filterHeight, topOffset],
    ([newFilterHeight, newTopOffset]) => {
      emit('dimensions-update', {
        filterHeight: newFilterHeight,
        topOffset: newTopOffset,
      });
    },
    { immediate: true }
  );

  // 暴露方法给父组件
  defineExpose({
    isSticky,
    filterHeight,
    topOffset,
    updateDimensions,
    scrollTop,
    stickyThreshold,
  });

  // 生命周期
  onMounted(async () => {
    await initStickyFilter();
  });

  onUnmounted(() => {
    destroyStickyFilter();
  });
</script>

<style lang="less" scoped>
  .sticky-filter-wrapper {
    position: relative;

    .filter-container {
      .filter-content {
        .filter-inner {
          transition: all 0.3s ease;
        }

        // 吸顶状态样式
        &.is-sticky {
          transition: all 0.3s ease;
        }
      }
    }

    .filter-placeholder {
      background: transparent;
    }
  }

  // 吸顶筛选器动画
  .filter-content {
    &.sticky-enter {
      animation: stickyEnter 0.4s ease-out;
    }
  }

  // 吸顶进入动画
  @keyframes stickyEnter {
    0% {
      transform: translateY(-20px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        // 移动端保持全宽，但添加边距
        left: 8px !important;
        right: 8px !important;
        width: auto !important;
        border-radius: 12px !important;
      }
    }
  }

  @media (max-width: 480px) {
    .sticky-filter-wrapper {
      .filter-content.is-sticky {
        left: 4px !important;
        right: 4px !important;
      }
    }
  }
</style>
