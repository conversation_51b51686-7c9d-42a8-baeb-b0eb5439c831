// 线索跟进状态 mock数据

import { ChartConfig } from '../types/statisticDashboard';
import { calculatePercentage, createTooltipFormatter, generateChartDataItem } from '../utils';
import { I18nGlobalTranslation, useI18n } from '/@/hooks/web/useI18n';
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

const { t } = useI18n('common');

/**
 * 线索跟进状态配置
 */
export interface FollowUpStatusConfig {
  key: string;
  name: string;
  color: string;
}

/**
 * 线索跟进状态配置
 */
export const generateFollowUpStatusConfig: (useI18nClue: { t: I18nGlobalTranslation }) => FollowUpStatusConfig[] = (useI18nClue: {
  t: I18nGlobalTranslation;
}) => {
  return [
    { key: 'storeAppointmentInProgress', name: t('inStoreReservationInProgress'), color: '#5470c6' }, // 蓝色
    { key: 'pendingDistribution', name: t('pendingDistribution'), color: '#91cc75' }, // 绿色
    { key: 'pendingAllocation', name: useI18nClue.t('clueCenter.info.toBeAssigned'), color: '#fac858' }, // 黄色
    { key: 'notFollowedUp', name: t('notFollowedUp'), color: '#ee6666' }, // 红色
    { key: 'appointedToStore', name: useI18nClue.t('clueCenter.info.reservationIsAvailable'), color: '#73c0de' }, // 青色
    { key: 'arrivedAtStore', name: t('testedAtTheStore'), color: '#3ba272' }, // 深绿色
    { key: 'depositMade', name: useI18nClue.t('clueCenter.info.decided'), color: '#fc8452' }, // 橙色
    { key: 'dealClosed', name: useI18nClue.t('clueCenter.info.filled'), color: '#9a60b4' }, // 紫色
    { key: 'defeated', name: useI18nClue.t('clueCenter.info.defeated'), color: '#ea7ccc' }, // 粉色
    { key: 'followUpInvalid', name: t('invalidFollowUp'), color: '#5d7092' }, // 深色蓝色
    { key: 'recycleDefeated', name: t('recycleDefeated'), color: '#b87333' }, // 棕色
  ];
};

/**
 * 日期线索跟进数据接口
 */
export interface DailyFollowUpData {
  name: string; // 日期名称
  value: number; // 总值
  type: 'period';
  statuses: Record<string, number>; // 各状态数据
  followUpRate: number; // 跟进率百分比
}

/**
 * 线索跟进状态数据（7天数据，对应图片中的日期范围）
 */
export const followUpOnCluesData: DailyFollowUpData[] = [
  {
    name: '2025-07-25',
    value: 387,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 78,
      pendingDistribution: 45,
      pendingAllocation: 52,
      notFollowedUp: 85,
      appointedToStore: 32,
      arrivedAtStore: 28,
      depositMade: 24,
      dealClosed: 20,
      defeated: 12,
      followUpInvalid: 8,
      recycleDefeated: 3,
    },
    followUpRate: 55.0, // (已跟进相关状态的总和 / 总数) * 100
  },
  {
    name: '2025-07-26',
    value: 432,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 94,
      pendingDistribution: 74,
      pendingAllocation: 54,
      notFollowedUp: 55,
      appointedToStore: 74,
      arrivedAtStore: 34,
      depositMade: 24,
      dealClosed: 20,
      defeated: 15,
      followUpInvalid: 10,
      recycleDefeated: 4,
    },
    followUpRate: 58.0,
  },
  {
    name: '2025-07-27',
    value: 398,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 68,
      pendingDistribution: 82,
      pendingAllocation: 48,
      notFollowedUp: 65,
      appointedToStore: 52,
      arrivedAtStore: 30,
      depositMade: 22,
      dealClosed: 18,
      defeated: 10,
      followUpInvalid: 7,
      recycleDefeated: 2,
    },
    followUpRate: 52.0,
  },
  {
    name: '2025-07-28',
    value: 457,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 94,
      pendingDistribution: 74,
      pendingAllocation: 54,
      notFollowedUp: 55,
      appointedToStore: 74,
      arrivedAtStore: 34,
      depositMade: 24,
      dealClosed: 20,
      defeated: 18,
      followUpInvalid: 12,
      recycleDefeated: 6,
    },
    followUpRate: 60.0,
  },
  {
    name: '2025-07-29',
    value: 246,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 58,
      pendingDistribution: 32,
      pendingAllocation: 28,
      notFollowedUp: 35,
      appointedToStore: 38,
      arrivedAtStore: 22,
      depositMade: 15,
      dealClosed: 12,
      defeated: 6,
      followUpInvalid: 4,
      recycleDefeated: 2,
    },
    followUpRate: 62.0,
  },
  {
    name: '2025-07-30',
    value: 523,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 125,
      pendingDistribution: 95,
      pendingAllocation: 68,
      notFollowedUp: 45,
      appointedToStore: 85,
      arrivedAtStore: 42,
      depositMade: 28,
      dealClosed: 22,
      defeated: 20,
      followUpInvalid: 15,
      recycleDefeated: 8,
    },
    followUpRate: 59.0,
  },
  {
    name: '2025-07-31',
    value: 456,
    type: 'period',
    statuses: {
      storeAppointmentInProgress: 98,
      pendingDistribution: 78,
      pendingAllocation: 58,
      notFollowedUp: 48,
      appointedToStore: 72,
      arrivedAtStore: 38,
      depositMade: 26,
      dealClosed: 20,
      defeated: 14,
      followUpInvalid: 10,
      recycleDefeated: 4,
    },
    followUpRate: 58.0,
  },
];

/**
 * 线索跟进状态tooltip格式化函数
 */
const formatFollowUpTooltip = createTooltipFormatter({
  showPercentage: true,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('clueCenter.info.followUpRate') },
  extraInfoProvider: (axisValue: string) => {
    // 查找对应日期的数据来获取跟进率和汇总信息
    const dayData = followUpOnCluesData.find((item) => item.name === axisValue);
    if (!dayData) return '';
    const useI18nClue = useI18nWithModule('clue-vue');
    // 计算跟进中的总数（排除未跟进的状态）
    const followingUpCount = Object.keys(dayData.statuses)
      .filter((key) => key !== 'notFollowedUp')
      .reduce((sum, key) => sum + (dayData.statuses[key] || 0), 0);

    const followingUpPercentage = dayData.value > 0 ? ((followingUpCount / dayData.value) * 100).toFixed(1) : '0.0';

    return [
      `${t('total')}：${dayData.value}`,
      `${useI18nClue.t('clueCenter.info.followUp')}：${followingUpCount} (${followingUpPercentage}%)`,
      `${useI18nClue.t('clueCenter.info.followUpRate')}：${dayData.followUpRate}%`,
    ].join('<br/>');
  },
});

/**
 * 生成线索跟进状态图表配置
 */
export const generateFollowUpOnCluesChartConfig = (): ChartConfig => {
  const data = followUpOnCluesData;
  const useI18nClue = useI18nWithModule('clue-vue');
  // 生成堆叠系列配置（包含所有状态）
  const stackSeries = generateFollowUpStatusConfig(useI18nClue).map((statusItem) => ({
    name: statusItem.name,
    type: 'bar',
    stack: 'followUp', // 堆叠配置
    data: data.map((item) => {
      const value = item.statuses[statusItem.key] || 0;
      const percent = item.value > 0 ? calculatePercentage(value, item.value) : '0.0';
      return generateChartDataItem(item.name, value, percent, {
        statusKey: statusItem.key,
      });
    }),
    itemStyle: {
      color: statusItem.color,
    },
    label: {
      show: false, // 在堆叠图中通常不显示标签，避免重叠
    },
  }));

  // 生成跟进率趋势线系列配置
  const followUpRateSeries = {
    name: useI18nClue.t('clueCenter.info.followUpRate'),
    type: 'line',
    yAxisIndex: 1, // 使用右侧Y轴
    data: data.map((item, index) => ({
      name: item.name,
      value: item.followUpRate,
      // 只在头尾显示标签
      label: {
        show: index === 0 || index === data.length - 1,
        position: 'top',
        formatter: '{c}%',
        fontSize: 12,
        color: '#8B0000', // 深红色标签
      },
    })),
    itemStyle: {
      color: '#8B0000', // 深红色数据点
    },
    lineStyle: {
      color: '#8B0000', // 深红色线条
      width: 2,
    },
    symbol: 'circle',
    symbolSize: 6,
  };

  // 合并所有系列
  const series = [...stackSeries, followUpRateSeries];

  return {
    id: 'followUpOnClues',
    type: 'bar',
    title: t('followUpStatusOfClues'),
    dataSource: 'followUpOnClues',
    loadingStrategy: 'followUpOnClues',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: [...generateFollowUpStatusConfig(useI18nClue).map((config) => config.color), '#8B0000'], // 包含所有状态和深红色趋势线

      grid: {
        left: '4%',
        right: '8%', // 为右侧Y轴留出更多空间
        bottom: '15%', // 为底部legend留出更多空间
        containLabel: true,
      },
      legend: {
        data: [...generateFollowUpStatusConfig(useI18nClue).map((config) => config.name), t('clueCenter.info.followUpRate')], // 包含所有状态和跟进率
        bottom: '5%', // legend放在底部
        left: 'center',
        type: 'scroll', // 如果legend项太多，支持滚动
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 30, // 倾斜30度
        },
      },
      yAxis: [
        {
          type: 'value',
          name: t('numberOfClues'),
          position: 'left',
        },
        {
          type: 'value',
          name: useI18nClue.t('clueCenter.info.followUpRate'),
          position: 'right',
          min: 0,
          max: 100,
          axisLabel: {
            formatter: '{value}%',
          },
        },
      ],
      series: series as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatFollowUpTooltip, // 使用专用的tooltip格式化函数
      },
    },
    size: { width: '100%', height: 500 },
    position: { x: 0, y: 0 },
  };
};

/**
 * 首次跟进时长区间配置
 */
export interface FollowUpTimeRangeConfig {
  key: string;
  name: string;
  color: string;
}

/**
 * 首次跟进时长区间配置
 */
export const followUpTimeRangeConfig: FollowUpTimeRangeConfig[] = [
  { key: '0-4h', name: '0-4小时', color: '#5470c6' }, // 蓝色
  { key: '4-8h', name: '4-8小时', color: '#5470c6' },
  { key: '8-12h', name: '8-12小时', color: '#5470c6' },
  { key: '12-16h', name: '12-16小时', color: '#5470c6' },
  { key: '16-20h', name: '16-20小时', color: '#5470c6' },
  { key: '20-24h', name: '20-24小时', color: '#5470c6' },
];

/**
 * 首次跟进时长分析数据接口
 */
export interface FirstFollowUpTimeAnalysisData {
  name: string; // 时长区间名称
  value: number; // 线索数量
  type: 'timeRange';
  trendRate: number; // 趋势率百分比
  percentage: number; // 占比百分比
}

/**
 * 首次跟进时长分析数据（根据图片数据）
 */
export const firstFollowUpTimeAnalysisData: FirstFollowUpTimeAnalysisData[] = [
  {
    name: '0-4小时',
    value: 118,
    type: 'timeRange',
    trendRate: 26.5,
    percentage: 26.5,
  },
  {
    name: '4-8小时',
    value: 92,
    type: 'timeRange',
    trendRate: 20.7,
    percentage: 20.7,
  },
  {
    name: '8-12小时',
    value: 67,
    type: 'timeRange',
    trendRate: 15.1,
    percentage: 15.1,
  },
  {
    name: '12-16小时',
    value: 35,
    type: 'timeRange',
    trendRate: 10.8,
    percentage: 10.8,
  },
  {
    name: '16-20小时',
    value: 35,
    type: 'timeRange',
    trendRate: 7.9,
    percentage: 7.9,
  },
  {
    name: '20-24小时',
    value: 29,
    type: 'timeRange',
    trendRate: 6.5,
    percentage: 6.5,
  },
  {
    name: '>24小时',
    value: 56,
    type: 'timeRange',
    trendRate: 12.6,
    percentage: 12.6,
  },
];

/**
 * 首次跟进时长分析tooltip格式化函数
 */
const formatFirstFollowUpTimeTooltip = createTooltipFormatter({
  showPercentage: false,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('proportionTrend') },
  extraInfoProvider: (axisValue: string) => {
    // 查找对应时长区间的数据来获取占比趋势
    const timeData = firstFollowUpTimeAnalysisData.find((item) => item.name === axisValue);
    const proportionTrend = timeData?.percentage || 0;
    return `${t('trendRate')}: ${proportionTrend}%`;
  },
});

/**
 * 生成首次跟进时长分析图表配置
 */
export const generateFirstFollowUpTimeAnalysisChartConfig = (): ChartConfig => {
  const data = firstFollowUpTimeAnalysisData;

  // 生成柱状图系列配置
  const barSeries = {
    name: t('numberOfClues'),
    type: 'bar',
    data: data.map((item) => {
      return generateChartDataItem(item.name, item.value, item.percentage.toString(), {
        timeRangeKey: item.name,
        trendRate: item.trendRate,
      });
    }),
    itemStyle: {
      color: '#5470c6', // 蓝色
    },
    label: {
      show: true,
      position: 'top',
      formatter: (params: any) => {
        // 显示数值和百分比
        return `${params.data.value} (${params.data.percent}%)`;
      },
      fontSize: 12,
      color: '#333',
    },
    barWidth: '60%', // 设置柱子宽度
  };

  // 生成占比趋势折线系列配置
  const trendLineSeries = {
    name: t('trendRate'),
    type: 'line',
    yAxisIndex: 1, // 使用右侧Y轴
    data: data.map((item) => ({
      name: item.name,
      value: item.trendRate,
      // 在所有点显示标签
      label: {
        show: true,
        position: 'top',
        formatter: '{c}%',
        fontSize: 12,
        color: '#ff4d4f', // 红色标签
      },
    })),
    itemStyle: {
      color: '#ff4d4f', // 红色数据点
    },
    lineStyle: {
      color: '#ff4d4f', // 红色线条
      width: 2,
    },
    symbol: 'circle',
    // symbolSize: 6,
  };

  return {
    id: 'firstFollowUpTimeAnalysis',
    type: 'bar',
    title: t('analysisOfInitialFollowUpDuration'),
    dataSource: 'firstFollowUpTimeAnalysis',
    loadingStrategy: 'firstFollowUpTimeAnalysis',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      color: ['#5470c6', '#ff4d4f'], // 蓝色柱状图和红色折线
      grid: {
        left: '8%',
        right: '8%', // 为右侧Y轴留出空间
        bottom: '15%',
        top: '15%',
        containLabel: true,
      },
      legend: {
        data: [t('numberOfClues'), t('trendRate')],
        bottom: '5%',
        left: 'center',
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.name),
        axisLabel: {
          interval: 0, // 强制显示所有标签
          rotate: 0, // 不旋转
          fontSize: 12,
        },
      },
      yAxis: [
        {
          type: 'value',
          name: t('numberOfClues'),
          position: 'left',
          axisLabel: {
            formatter: '{value}',
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
          },
        },
        {
          type: 'value',
          name: `${t('trendRate')}(%)`,
          position: 'right',
          min: 0,
          max: 30, // 根据图片数据调整最大值
          axisLabel: {
            formatter: '{value}%',
          },
          nameTextStyle: {
            fontSize: 12,
            color: '#666',
          },
        },
      ],
      series: [barSeries, trendLineSeries] as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatFirstFollowUpTimeTooltip,
      },
    },
    size: { width: '100%', height: 500 },
  };
};

/** 线索跟进状态图表配置 */
export const followUpOnCluesChartConfigs: ChartConfig[] = [generateFirstFollowUpTimeAnalysisChartConfig()];
