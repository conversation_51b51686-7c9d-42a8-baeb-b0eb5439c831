/**
 * 线索转化mock数据
 */

import type { ChartConfig } from '../types/statisticDashboard';
import { createTooltipFormatter } from '../utils';
import { useI18n } from '/@/hooks/web/useI18n';
import { useI18nWithModule } from '/@/hooks/web/useI18nEnhanced';

const { t } = useI18n('common');

/**
 * 线索转化阶段配置接口
 */
export interface LeadConversionStageConfig {
  /** 阶段名称 */
  name: string;
  /** 阶段数量 */
  value: number;
  /** 转化率 */
  conversionRate: string;
  /** 阶段颜色 */
  color?: string;
}

/**
 * 线索转化分析数据接口
 */
export interface LeadConversionAnalysisData {
  /** 转化阶段数据 */
  stages: LeadConversionStageConfig[];
  /** 总线索数 */
  totalLeads: number;
  /** 最终转化率 */
  finalConversionRate: string;
}

const generateLeadConversionAnalysisMockData = () => {
  const i18nClue = useI18nWithModule('clue-vue');
  return {
    stages: [
      {
        name: t('totalLeads'),
        value: 1200,
        conversionRate: '100%',
        color: '#1890ff',
      },
      {
        name: t('validLeads'),
        value: 1050,
        conversionRate: '87.5%',
        color: '#1890ff',
      },
      {
        name: t('pendingDistribution'),
        value: 1050,
        conversionRate: '87.5%',
        color: '#1890ff',
      },
      {
        name: i18nClue.t('clueCenter.info.toBeAssigned'),
        value: 986,
        conversionRate: '82.2%',
        color: '#1890ff',
      },
      {
        name: i18nClue.t('clueCenter.info.toBeFollowedUp'),
        value: 850,
        conversionRate: '70.8%',
        color: '#1890ff',
      },
      {
        name: t('reserved'),
        value: 720,
        conversionRate: '60.0%',
        color: '#1890ff',
      },
      {
        name: t('arrivedAtTheStore'),
        value: 650,
        conversionRate: '54.2%',
        color: '#1890ff',
      },
      {
        name: t('testedIt'),
        value: 320,
        conversionRate: '26.7%',
        color: '#1890ff',
      },
      {
        name: i18nClue.t('clueCenter.info.decided'),
        value: 156,
        conversionRate: '13.0%',
        color: '#1890ff',
      },
      {
        name: i18nClue.t('clueCenter.info.filled'),
        value: 75,
        conversionRate: '6.2%',
        color: '#1890ff',
      },
    ],
    totalLeads: 1300,
    finalConversionRate: '13.0%',
  };
};

/**
 * 线索转化分析mock数据
 */
export const leadConversionAnalysisData: LeadConversionAnalysisData = generateLeadConversionAnalysisMockData();

const formatter = createTooltipFormatter({
  showPercentage: false,
  extraInfoProvider: (itemName: string) => {
    const currentStageIndex = leadConversionAnalysisData.stages.findIndex((s) => s.name === itemName);
    if (currentStageIndex === -1) return '';

    const currentStage = leadConversionAnalysisData.stages[currentStageIndex];

    const tooltipContent = `<div style="color: #666; font-size: 12px;">${t('conversionRate')}：${currentStage.conversionRate}</div>`;

    return tooltipContent;
  },
  valueFormatter: (value: number) => value.toString(),
});

/**
 * 生成线索转化分析图表配置
 */
export const generateLeadConversionAnalysisChartConfig = (): ChartConfig => {
  const data = leadConversionAnalysisData;

  // 转换数据格式为ECharts漏斗图所需格式，保留转化率信息用于tooltip显示
  const chartData = data.stages.map((stage) => ({
    name: stage.name,
    value: stage.value,
    conversionRate: stage.conversionRate, // 添加转化率字段供tooltip使用
    itemStyle: {
      color: stage.color || '#1890ff',
    },
  }));

  return {
    loadingStrategy: 'static',
    id: 'lead-conversion-analysis',
    type: 'funnel',
    title: t('clueConversionAnalysis'),
    dataSource: 'leadConversionAnalysis',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'item',
        formatter: formatter,
      },
      legend: {
        show: true,
        data: data.stages.map((stage) => stage.name),
        bottom: '5%', // legend放在底部
        left: 'center',
        type: 'scroll', // 如果legend项太多，支持滚动
      },
      series: [
        {
          name: t('leadConversion'),
          type: 'funnel',
          left: '10%',
          top: 60,
          bottom: 60,
          width: '80%',
          min: 0,
          max: data.totalLeads,
          minSize: '0%',
          maxSize: '100%',
          sort: 'descending',
          gap: 2,
          label: {
            show: true,
            position: 'inside',
            formatter: (params: any) => {
              const stage = data.stages.find((s) => s.name === params.name);
              return stage ? `${params.name}\n${params.value}` : params.name;
            },
            fontSize: 12,
            color: '#fff',
          },
          labelLine: {
            length: 10,
            lineStyle: {
              width: 1,
              type: 'solid',
            },
          },
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 1,
          },
          emphasis: {
            label: {
              fontSize: 14,
            },
          },
          data: chartData,
        },
      ],
    },
    // 测试
    layout: {
      // maxWidth: '80%',
    },
  };
};

/**
 * 车型成交周期配置接口
 */
export interface VehicleModelDealCycleConfig {
  /** 车型名称 */
  name: string;
  /** 成交周期（天） */
  dealCycle: number;
  /** 车型颜色 */
  color?: string;
}

/**
 * 各车型成交分析数据接口
 */
export interface VehicleModelDealAnalysisData {
  /** 车型数据 */
  models: VehicleModelDealCycleConfig[];
  /** 平均成交周期 */
  averageDealCycle: number;
}

/**
 * 各车型成交分析mock数据
 */
export const vehicleModelDealAnalysisData: VehicleModelDealAnalysisData = {
  models: [
    {
      name: 'GA4车型',
      dealCycle: 15,
      color: '#5470c6',
    },
    {
      name: 'HY2车型',
      dealCycle: 22,
      color: '#5470c6',
    },
    {
      name: '第三车型',
      dealCycle: 19,
      color: '#5470c6',
    },
    {
      name: 'HY3车型',
      dealCycle: 25,
      color: '#5470c6',
    },
    {
      name: 'M3车型',
      dealCycle: 12,
      color: '#5470c6',
    },
    {
      name: 'C5车型',
      dealCycle: 20,
      color: '#5470c6',
    },
    {
      name: 'M2车型',
      dealCycle: 16,
      color: '#5470c6',
    },
    {
      name: '第八车型',
      dealCycle: 28,
      color: '#5470c6',
    },
    {
      name: 'M1车型',
      dealCycle: 14,
      color: '#5470c6',
    },
    {
      name: 'C3车型',
      dealCycle: 19,
      color: '#5470c6',
    },
    {
      name: 'C2车型',
      dealCycle: 24,
      color: '#5470c6',
    },
  ],
  averageDealCycle: 20.3,
};

/**
 * 车型成交周期tooltip格式化函数
 */
const formatVehicleModelDealTooltip = createTooltipFormatter({
  showPercentage: false,
  excludeSeriesTypes: ['line'],
  specialSeries: { type: 'line', label: t('averageTransactionCycle') },
  extraInfoProvider: (axisValue: string) => {
    // 查找对应车型的数据
    const modelData = vehicleModelDealAnalysisData.models.find((model) => model.name === axisValue);
    if (!modelData) return '';

    // const averageCycle = vehicleModelDealAnalysisData.averageDealCycle;

    return [`${t('transactionCycle')}：${modelData.dealCycle}${t('time_days')}`].join('<br/>');
  },
  valueFormatter: (value: number) => `${value}${t('time_days')}`,
});

/**
 * 生成各车型成交分析图表配置
 */
export const generateVehicleModelDealAnalysisChartConfig = (): ChartConfig => {
  const data = vehicleModelDealAnalysisData;
  const i18nClue = useI18nWithModule('clue-vue');
  // 生成柱状图系列配置
  const barSeries = {
    name: t('transactionCycle'),
    type: 'bar',
    data: data.models.map((model) => ({
      name: model.name,
      value: model.dealCycle,
      itemStyle: {
        color: model.color || '#5470c6',
      },
    })),
    label: {
      show: true,
      position: 'top',
      formatter: (params: any) => `${params.data.value}天`,
      fontSize: 12,
      color: '#333',
    },
    barWidth: '60%',
  };

  // 生成平均线系列配置
  const averageLineSeries = {
    name: t('averageTransactionCycle'),
    type: 'line',
    data: data.models.map(() => ({
      value: data.averageDealCycle,
    })),
    lineStyle: {
      color: '#1890ff',
      width: 2,
      type: 'dashed',
    },
    itemStyle: {
      color: '#1890ff',
    },
    symbol: 'none', // 不显示数据点
    label: {
      show: false,
    },
    markLine: {
      silent: true,
      symbol: 'none',
      label: {
        show: true,
        position: 'end',
        formatter: `${t('average')}：${data.averageDealCycle}${t('time_days')}`,
        color: '#1890ff',
        fontSize: 12,
        fontWeight: 'bold',
        offset: [-10, 0],
      },
      lineStyle: {
        color: '#1890ff',
        width: 1,
        type: 'dashed',
      },
      data: [
        {
          yAxis: data.averageDealCycle,
        },
      ],
    },
  };

  return {
    id: 'vehicleModelDealAnalysis',
    type: 'bar',
    title: t('vehicleModelDealAnalysis'),
    dataSource: 'vehicleModelDealAnalysis',
    loadingStrategy: 'vehicleModelDealAnalysis',
    options: {
      // 禁用ECharts内置标题，使用自定义标题
      title: {
        show: false,
      },
      // 顶部显示平均成交周期
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: '5%',
          style: {
            text: `${t('averageTransactionCycle')}：${data.averageDealCycle}${t('time_days')}`,
            fontSize: 14,
            fontWeight: 'bold',
            fill: '#333',
          },
        },
      ],
      color: ['#5470c6', '#ff4d4f'],
      grid: {
        left: '12%',
        right: '12%',
        bottom: '5%',
        top: '20%', // 为顶部文字留出空间
        containLabel: true,
      },
      legend: {
        data: [t('transactionCycle'), t('averageTransactionCycle')],
        bottom: '5%',
        left: 'center',
        show: false,
      },
      xAxis: {
        type: 'category',
        data: data.models.map((model) => model.name),
        axisLabel: {
          interval: 0,
          rotate: 30, // 倾斜30度避免重叠
          fontSize: 12,
        },
        name: i18nClue.t('clue.order.model.name'),
        nameLocation: 'end',
        nameTextStyle: {
          fontSize: 12,
          color: '#666',
          padding: [0, 0, 0, 0],
        },
        // 在X轴最右边添加平均值标记
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#d9d9d9',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: `${t('transactionCycle')}`,
        nameTextStyle: {
          fontSize: 12,
          color: '#666',
        },
        axisLabel: {
          formatter: (value: number) => `${value}${t('time_days')}`,
        },
        splitLine: {
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series: [barSeries, averageLineSeries] as any,
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: formatVehicleModelDealTooltip,
      },
    },
    layout: {
      // columnSpan: 1,
      // maxWidth: '100%',
    },
  };
};

export const leadConversionChartConfig = generateLeadConversionAnalysisChartConfig();
export const vehicleModelDealChartConfig = generateVehicleModelDealAnalysisChartConfig();
