/**
 * 多模块国际化资源管理器
 * 解决多个模块的国际化资源冲突问题，支持模块化的资源加载和管理
 *
 * 设计原则：
 * 1. 不影响现有的 common 命名空间逻辑
 * 2. 支持多模块资源的独立管理
 * 3. 提供统一的资源访问接口
 * 4. 支持资源的动态加载和缓存
 */

import { MULTILINGUAT_KEY } from '/@/enums/cacheEnum';
import { getMultiLingual, multiLingualResourceType } from '/@/api/common/api';

/**
 * 多语言资源数据结构
 */
export interface MultiLingualResource {
  [namespace: string]: {
    [key: string]: {
      [language: string]: string;
    };
  };
}

/**
 * 模块资源映射配置
 * 将不同的 resourceType 映射到不同的命名空间，避免冲突
 */
const MODULE_NAMESPACE_MAP: Record<multiLingualResourceType, string> = {
  UNIFIED_FRONT_END: 'common', // 保持现有逻辑不变
  'clue-vue': 'clue', // 线索模块使用 clue 命名空间
  'work-order-vue': 'workOrder', // 工单模块使用 workOrder 命名空间
};

/**
 * 多模块国际化资源管理器
 */
export class MultiModuleI18nManager {
  private static instance: MultiModuleI18nManager;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): MultiModuleI18nManager {
    if (!MultiModuleI18nManager.instance) {
      MultiModuleI18nManager.instance = new MultiModuleI18nManager();
    }
    return MultiModuleI18nManager.instance;
  }

  /**
   * 获取当前缓存的多语言资源
   */
  private getCurrentResources(): MultiLingualResource {
    const languageStr = localStorage.getItem(MULTILINGUAT_KEY);
    return languageStr ? JSON.parse(languageStr) : {};
  }

  /**
   * 保存多语言资源到缓存
   */
  private saveResources(resources: MultiLingualResource): void {
    localStorage.setItem(MULTILINGUAT_KEY, JSON.stringify(resources));
  }

  /**
   * 将API返回的数据转换为标准格式
   */
  private transformApiData(data: any[], resourceType: multiLingualResourceType): MultiLingualResource {
    const namespace = MODULE_NAMESPACE_MAP[resourceType];
    const result: MultiLingualResource = {
      [namespace]: {},
    };

    data.forEach((item: any) => {
      result[namespace][item.code] = {};
      item?.valueLanguageResponses?.forEach((langItem: any) => {
        result[namespace][item.code][langItem.language] = langItem.value || undefined;
      });
    });

    return result;
  }

  /**
   * 合并多语言资源
   */
  private mergeResources(existing: MultiLingualResource, newResources: MultiLingualResource): MultiLingualResource {
    const merged = { ...existing };

    Object.keys(newResources).forEach((namespace) => {
      if (!merged[namespace]) {
        merged[namespace] = {};
      }
      merged[namespace] = { ...merged[namespace], ...newResources[namespace] };
    });

    return merged;
  }

  /**
   * 加载指定模块的多语言资源
   * @param resourceType 资源类型
   * @param forceReload 是否强制重新加载（已废弃，现在总是重新加载）
   */
  public async loadModuleResources(resourceType: multiLingualResourceType): Promise<void> {
    // 移除缓存检查逻辑，每次都重新加载以确保多语言资源是最新的
    console.log(`🔄 开始加载模块 ${resourceType} 的多语言资源...`);

    // 直接执行加载，不再缓存Promise
    await this.performLoad(resourceType);
    console.log(`✅ 模块 ${resourceType} 的多语言资源加载完成`);
  }

  /**
   * 执行实际的资源加载
   */
  private async performLoad(resourceType: multiLingualResourceType): Promise<void> {
    try {
      const data = await getMultiLingual({ resourceType });

      if (data?.length) {
        const newResources = this.transformApiData(data, resourceType);
        const currentResources = this.getCurrentResources();
        const mergedResources = this.mergeResources(currentResources, newResources);

        this.saveResources(mergedResources);

        // 触发全局事件，通知资源已更新
        window.dispatchEvent(
          new CustomEvent('i18n-resources-updated', {
            detail: { resourceType, namespace: MODULE_NAMESPACE_MAP[resourceType] },
          })
        );
      } else {
        console.warn(`⚠️ 模块 ${resourceType} 没有返回多语言数据`);
      }
    } catch (error) {
      console.error(`❌ 加载模块 ${resourceType} 的多语言资源失败:`, error);
      throw error;
    }
  }

  /**
   * 批量加载多个模块的资源
   */
  public async loadMultipleModules(resourceTypes: multiLingualResourceType[]): Promise<void> {
    const loadPromises = resourceTypes.map((type) => this.loadModuleResources(type));

    await Promise.all(loadPromises);
  }

  /**
   * 检查指定模块的资源是否已加载
   */
  public isModuleLoaded(resourceType: multiLingualResourceType): boolean {
    const namespace = MODULE_NAMESPACE_MAP[resourceType];
    const currentResources = this.getCurrentResources();
    return !!(currentResources[namespace] && Object.keys(currentResources[namespace]).length > 0);
  }

  /**
   * 获取指定模块的命名空间
   */
  public getModuleNamespace(resourceType: multiLingualResourceType): string {
    return MODULE_NAMESPACE_MAP[resourceType];
  }

  /**
   * 清除指定模块的资源缓存
   */
  public clearModuleResources(resourceType: multiLingualResourceType): void {
    const namespace = MODULE_NAMESPACE_MAP[resourceType];
    const currentResources = this.getCurrentResources();
    console.log('currentResources', currentResources);
    if (currentResources[namespace]) {
      delete currentResources[namespace];
      this.saveResources(currentResources);
      console.log(`🗑️ 已清除模块 ${resourceType} 的多语言资源缓存`);
    }
  }

  /**
   * 获取所有已加载的模块信息
   */
  public getLoadedModules(): { resourceType: multiLingualResourceType; namespace: string; keyCount: number }[] {
    const currentResources = this.getCurrentResources();
    const result: { resourceType: multiLingualResourceType; namespace: string; keyCount: number }[] = [];

    Object.entries(MODULE_NAMESPACE_MAP).forEach(([resourceType, namespace]) => {
      if (currentResources[namespace]) {
        result.push({
          resourceType: resourceType as multiLingualResourceType,
          namespace,
          keyCount: Object.keys(currentResources[namespace]).length,
        });
      }
    });

    return result;
  }
}

/**
 * 导出单例实例
 */
export const multiModuleI18nManager = MultiModuleI18nManager.getInstance();

/**
 * 便捷的模块资源加载函数
 */
export const loadI18nModule = (resourceType: multiLingualResourceType) => {
  return multiModuleI18nManager.loadModuleResources(resourceType);
};

/**
 * 便捷的批量模块资源加载函数
 */
export const loadI18nModules = (resourceTypes: multiLingualResourceType[]) => {
  return multiModuleI18nManager.loadMultipleModules(resourceTypes);
};
