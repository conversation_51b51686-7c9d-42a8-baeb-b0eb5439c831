/**
 * 统计维度类型枚举
 */
export enum StatisticalDimensionTypeEnum {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  SEASON = 'SEASON', // 季度统计维度，与后端保持一致
  YEAR = 'YEAR',
}

// 线索来源枚举值,区分一级二级，线上线下，公域私域
export enum ClueSourceEnum {
  /** 一级来源线上公域 */
  ONE_SOURCE_ONLINE_PUB = '1905168849350201344',
  /** 一级来源线上私域 */
  ONE_SOURCE_ONLINE_PRI = '1904778848922349568',
  /** 一级来源线下私域 */
  ONE_SOURCE_OFFLINE_PRI = '1905168967029309440',
  /** 二级来源线上私域-app */
  TWO_SOURCE_ONLINE_PRI_APP = '1905169100518133760',
  /** 二级来源线上私域-官网 */
  TWO_SOURCE_ONLINE_PRI_WEB = '1905169217949048832',
  /** 二级来源线上私域-客服中心 */
  TWO_SOURCE_ONLINE_PRI_CUSTOM = '1905169353227505664',
  /** 二级来源线下私域-营销活动 */
  TWO_SOURCE_OFFLINE_PRI_MARKETING = '1905169832407232512',
  /** 二级来源线下私域-车展 */
  TWO_SOURCE_OFFLINE_PRI_CARSHOW = '1905169979268497408',
  /** 二级来源线上公域-社媒 */
  TWO_SOURCE_ONLINE_PUB_SOCIAL = '1905169632522022912',
  /** 二级来源线上公域-垂媒 */
  TWO_SOURCE_ONLINE_PUB_VERTICAL = '1905169687684657152',
}
